<?php

$inputDir = __DIR__ . '/photos';
$outputDir = __DIR__ . '/thumb';
$maxSize = 500;

// Create output folder if it doesn't exist
if (!file_exists($outputDir)) {
    mkdir($outputDir, 0777, true);
}

// Get all .jpg files
$files = glob($inputDir . '/*.jpg');

foreach ($files as $filePath) {
    $fileName = basename($filePath);
    $srcImg = imagecreatefromjpeg($filePath);
    if (!$srcImg) continue;

    $width = imagesx($srcImg);
    $height = imagesy($srcImg);

    // Calculate new dimensions
    if ($width >= $height) {
        $newWidth = $maxSize;
        $newHeight = intval($height * ($maxSize / $width));
    } else {
        $newHeight = $maxSize;
        $newWidth = intval($width * ($maxSize / $height));
    }

    $dstImg = imagecreatetruecolor($newWidth, $newHeight);
    imagecopyresampled($dstImg, $srcImg, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

    // Save resized image
    imagejpeg($dstImg, $outputDir . '/' . $fileName, 90);

    imagedestroy($srcImg);
    imagedestroy($dstImg);

    echo "Resized: $fileName\n";
}

echo "Done.\n";
