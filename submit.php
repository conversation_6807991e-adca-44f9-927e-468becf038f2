<?php
header("Content-Type: application/json");

$sendgrid_api_key = '*********************************************************************';
$admin_recipients = ['<EMAIL>', '<EMAIL>'];
$from = [
    'email' => '<EMAIL>',
    'name' => 'Fl<PERSON>ra és Ádám'
];

try {
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data || !isset($data['guests'])) {
        http_response_code(400);
        echo json_encode(["error" => "Invalid input."]);
        exit;
    }

    $lang = strtolower($data['lang'] ?? 'hu');
    if (!in_array($lang, ['hu', 'en'])) $lang = 'hu';

    $db = new PDO('sqlite:rsvp.sqlite');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $db->exec("
        CREATE TABLE IF NOT EXISTS rsvps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contact TEXT,
            accomodation TEXT,
            special_request TEXT,
            child_chair INTEGER,
            submitted_at TEXT
        );
        CREATE TABLE IF NOT EXISTS guests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            submission_id INTEGER,
            name TEXT,
            diet_lactose INTEGER,
            diet_gluten INTEGER,
            diet_sugar INTEGER,
            diet_vegetarian INTEGER,
            diet_vegan INTEGER,
            special_diet TEXT,
            allergy TEXT,
            FOREIGN KEY(submission_id) REFERENCES rsvps(id)
        );
    ");

    $db->beginTransaction();

    $stmt = $db->prepare("
        INSERT INTO rsvps (contact, accomodation, special_request, child_chair, submitted_at)
        VALUES (:contact, :accomodation, :special_request, :child_chair, datetime('now'))
    ");
    $stmt->execute([
        ':contact' => $data['contact'] ?? '',
        ':accomodation' => $data['accomodation'] ?? '',
        ':special_request' => $data['special_request'] ?? '',
        ':child_chair' => !empty($data['child_chair']) ? 1 : 0
    ]);

    $submission_id = $db->lastInsertId();

    $guestStmt = $db->prepare("
        INSERT INTO guests (
            submission_id, name, diet_lactose, diet_gluten, diet_sugar, diet_vegetarian, diet_vegan, special_diet, allergy
        ) VALUES (
            :submission_id, :name, :diet_lactose, :diet_gluten, :diet_sugar, :diet_vegetarian, :diet_vegan, :special_diet, :allergy
        )
    ");

    foreach ($data['guests'] as $guest) {
        $guestStmt->execute([
            ':submission_id' => $submission_id,
            ':name' => $guest['name'] ?? '',
            ':diet_lactose' => !empty($guest['diet_lactose']) ? 1 : 0,
            ':diet_gluten' => !empty($guest['diet_gluten']) ? 1 : 0,
            ':diet_sugar' => !empty($guest['diet_sugar']) ? 1 : 0,
            ':diet_vegetarian' => !empty($guest['diet_vegetarian']) ? 1 : 0,
            ':diet_vegan' => !empty($guest['diet_vegan']) ? 1 : 0,
            ':special_diet' => $guest['special_diet'] ?? '',
            ':allergy' => $guest['allergy'] ?? ''
        ]);
    }

    $db->commit();

    $etetoszek = !empty($data['child_chair']) ? ($lang === 'hu' ? 'Igen' : 'Yes') : ($lang === 'hu' ? 'Nem' : 'No');

    // Vendégek HTML táblázat
    $guestTable = '<table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;margin-top:10px;">';
    $guestTable .= '<tr><th>#</th><th>Név / Name</th><th>Diéta / Diet</th><th>Speciális / Special</th><th>Allergia / Allergy</th></tr>';
    foreach ($data['guests'] as $i => $guest) {
        $diet = [];
        foreach (['lactose','gluten','sugar','vegetarian','vegan'] as $d) {
            if (!empty($guest["diet_$d"])) $diet[] = ucfirst($d);
        }
        $guestTable .= '<tr>';
        $guestTable .= '<td>' . ($i + 1) . '</td>';
        $guestTable .= '<td>' . htmlspecialchars($guest['name'] ?? '') . '</td>';
        $guestTable .= '<td>' . htmlspecialchars(implode(', ', $diet)) . '</td>';
        $guestTable .= '<td>' . htmlspecialchars($guest['special_diet'] ?? '') . '</td>';
        $guestTable .= '<td>' . htmlspecialchars($guest['allergy'] ?? '') . '</td>';
        $guestTable .= '</tr>';
    }
    $guestTable .= '</table>';

    // Email body (vendégnek)
    if ($lang === 'hu') {
        $subject = 'Köszönjük a visszajelzést! 🎉';
        $body = <<<HTML
<p>Kedves Vendégünk!</p>
<p>Köszönjük, hogy visszajeleztél az esküvőnkre. Nagyon örülünk, hogy ott leszel!</p>
<p><strong>Kapcsolattartó:</strong> {$data['contact']}<br>
<strong>Szálláskérés:</strong> {$data['accomodation']}<br>
<strong>Megjegyzés:</strong> {$data['special_request']}<br>
<strong>Gyerek etetőszék:</strong> {$etetoszek}</p>
<h3>Vendégek:</h3>
$guestTable
<p>Hamarosan további részletekkel jelentkezünk!</p>
<p>Üdvözlettel:<br><strong>Flóra és Ádám</strong></p>
HTML;
    } else {
        $subject = 'Thank you for your confirmation! 🎉';
        $body = <<<HTML
<p>Dear Guest,</p>
<p>Thank you for your RSVP to our wedding! We're so happy you'll be joining us!</p>
<p><strong>Contact:</strong> {$data['contact']}<br>
<strong>Accommodation request:</strong> {$data['accomodation']}<br>
<strong>Special note:</strong> {$data['special_request']}<br>
<strong>Child high chair:</strong> {$etetoszek}</p>
<h3>Guests:</h3>
$guestTable
<p>We'll be in touch with more details soon!</p>
<p>With love,<br><strong>Flóra & Ádám</strong></p>
HTML;
    }

    // Admin e-mail
    $adminSubject = 'Új visszajelzés érkezett';
    $adminBody = $body . '<hr><p><a href="https://fladam.hu/admin.php">Admin megtekintése</a></p>';

    send_email($sendgrid_api_key, $data['contact'], $subject, $body, $from, true);
    foreach ($admin_recipients as $adminEmail) {
        send_email($sendgrid_api_key, $adminEmail, $adminSubject, $adminBody, $from, true);
    }

    echo json_encode(["success" => true]);
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollBack();
    }
    http_response_code(500);
    echo json_encode(["error" => "Database error", "details" => $e->getMessage()]);
}

// SendGrid HTML email sender
function send_email($apiKey, $to, $subject, $bodyHtml, $from, $isHtml = false) {
    $email = [
        'personalizations' => [[
            'to' => [['email' => $to]],
            'subject' => $subject
        ]],
        'from' => [
            'email' => $from['email'],
            'name' => $from['name']
        ],
        'content' => [[
            'type' => $isHtml ? 'text/html' : 'text/plain',
            'value' => $bodyHtml
        ]]
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.sendgrid.com/v3/mail/send');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($email));
    $response = curl_exec($ch);
    if (curl_getinfo($ch, CURLINFO_HTTP_CODE) >= 400) {
        error_log("SendGrid error to $to: $response");
    }
    curl_close($ch);
}
