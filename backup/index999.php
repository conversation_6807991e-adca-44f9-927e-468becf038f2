<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && str_starts_with($_SERVER['CONTENT_TYPE'], 'application/json')) {
    $data = json_decode(file_get_contents('php://input'), true);
    echo "<pre>";
    echo "Feldolgozott vendégadatok:\n\n";
    print_r($data['guests'] ?? []);
    echo "</pre><hr />";
    exit;
}
?>

<!DOCTYPE html>
<html lang="hu">
<head>
  <meta charset="UTF-8">
  <title>Flóra & Ádám Esküvő Meghívó</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=EB+Garamond&display=swap" rel="stylesheet">
  <link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
  <script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: 'EB Garamond', serif;
      background: linear-gradient(135deg, #dfe9c4 0%, #cce0b1 50%, #e3e9cf 100%);
      color: #2f2f2f;
    }

    /* Parallax background section */
    .parallax-section {
      position: relative;
      height: 100vh;
      overflow: hidden;
      z-index: 1;
    }

    .parallax-background {
      position: sticky;
      top: 0;
      height: 100vh;
      width: 100%;
      z-index: 0;
    }

    .invite-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .parallax-text {
      position: absolute;
      top: 20%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      pointer-events: none;
      color: #2f2f2f;
      text-shadow: 1px 1px 3px rgba(255,255,255,0.8);
    }

    .parallax-text .name {
      font-family: 'Great Vibes', cursive;
      font-size: 3.5rem;
    }

    .parallax-text .date {
      font-family: 'EB Garamond', serif;
      font-size: 1.5rem;
      margin-top: 0.4rem;
    }

    /* Content container */
    .container {
      max-width: 900px;
      margin: 2rem auto 4rem auto;
      background: rgba(255, 255, 255, 0.9);
      border: 12px double #7c7a64;
      border-radius: 10px;
      box-shadow: 0 0 30px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .container-inner {
      padding: 4rem 2rem;
    }

    h2 {
      font-size: 1.3rem;
      text-align: center;
      margin-top: 0;
    }

    p {
      text-align: center;
      margin-bottom: 1.5rem;
      font-size: 1.05rem;
    }

    .section {
      margin-bottom: 2.5rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0 auto;
      max-width: 300px;
    }

    ul li {
      text-align: center;
      margin: 0.4em 0;
    }

    .gps {
      font-size: 0.9em;
      color: #555;
    }

    #map {
      width: 100%;
      height: 300px;
      border-radius: 8px;
      margin: 1rem 0;
    }

    .map-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 1rem;
    }

    .map-buttons a {
      background-color: #789262;
      color: white;
      padding: 0.6rem 1.2rem;
      border-radius: 6px;
      text-decoration: none;
      font-weight: bold;
      transition: background-color 0.3s;
    }

    .map-buttons a:hover {
      background-color: #5e734a;
    }

    .footer {
      font-style: italic;
      text-align: center;
      margin-top: 2rem;
      font-size: 0.95rem;
    }

    .highlight {
      font-weight: bold;
      font-size: 1.1rem;
    }

    form label {
      display: block;
      margin: 0.5rem 0 0.2rem;
    }

    form input[type="text"],
    form input[type="email"],
    form textarea {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: 'EB Garamond', serif;
      font-size: 1rem;
      margin-bottom: 1rem;
      box-sizing: border-box;
    }

    .participant {
      border: 1px solid #a6b98b;
      background: #f9f9f9;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .participant legend {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .checkbox-group label {
      display: inline-block;
      margin-right: 1rem;
      font-size: 0.95rem;
    }

    .actions {
      text-align: center;
      margin-top: 1.5rem;
    }

    .actions button {
      padding: 0.6rem 1.5rem;
      background-color: #789262;
      color: white;
      border: none;
      border-radius: 6px;
      font-weight: bold;
      font-size: 1rem;
      margin: 0.5rem;
      cursor: pointer;
    }

    .actions button:hover {
      background-color: #5e734a;
    }

    .remove-btn {
      float: right;
      background-color: #b15353;
      border: none;
      color: white;
      font-size: 0.8rem;
      padding: 0.2rem 0.6rem;
      border-radius: 4px;
      cursor: pointer;
    }

    .remove-btn:hover {
      background-color: #933;
    }
  </style>
</head>
<body>

  <!-- Main Content -->
  <div class="container">
    <div class="parallax-background">
      <img src="invite_front.jpg" alt="Esküvő Meghívó" class="invite-image">
      <div class="parallax-text">
        <div class="name">FLÓRA & ÁDÁM</div>
        <div class="date">2025.09.13.</div>
      </div>
    </div>
    <div class="container-inner">
      <p>Úgy döntöttünk, hogy most már örökre bírni fogjuk egymást.<br>
      Gyertek velünk nevetni, sírni, és tortát enni!</p>

      <div class="section">
        <h2>Program</h2>
        <ul>
          <li>14:30 – vendégvárás</li>
          <li>15:30 – ceremónia</li>
          <li>16:00 – fotózás</li>
          <li>19:00 – vacsora</li>
          <li>23:00 – torta</li>
          <li>0:00 – éjféli vacsora</li>
          <li>0:45 – moshpit hajnalig</li>
        </ul>
      </div>

      <div class="section">
        <h2>Cím</h2>
        <p>Szokolya, Királyrét, 2624<br>
          <span class="gps">47.89533180751165, 18.97720805857283</span>
        </p>
        <div id="map"></div>
        <div class="map-buttons">
          <a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank">📍 Google Térkép</a>
          <a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes" target="_blank">🚗 Waze útvonal</a>
        </div>
      </div>

      <div class="footer">
        A lakásárak elszálltak, de a szerelmünk változatlan. Ha nem turmixgépet hozol hanem borítékot, abba meg belefér egy négyzetméter!<br>
        <span class="highlight">Facebook csoport:</span> Flóra & Ádám házasodik
      </div>

      <div class="section">
        <h2>RSVP – Visszajelzés</h2>
        <form id="rsvp-form" onsubmit="handleSubmit(event)">
          <div id="participants-container"></div>
          <div class="actions">
            <button type="button" onclick="addParticipant()">Vendég hozzáadása</button>
            <button type="submit">Beküldés</button>
          </div>
        </form>
      </div>

      <template id="participant-template">
        <fieldset class="participant">
          <legend>Vendég <button type="button" class="remove-btn" onclick="removeParticipant(this)">mégse</button></legend>
          <label>Teljes név:</label>
          <input type="text" name="name[]" required>

          <label>Speciális étrend:</label>
          <div class="checkbox-group">
            <label><input type="checkbox" value="laktózmentes"> Laktózmentes</label>
            <label><input type="checkbox" value="gluténmentes"> Gluténmentes</label>
            <label><input type="checkbox" value="cukormentes"> Cukormentes</label>
            <label><input type="checkbox" value="vegetáriánus"> Vegetáriánus</label>
            <label><input type="checkbox" value="vegán"> Vegán</label>
          </div>
          <label>Egyéb:</label>
          <input type="text" name="diet_other[]" placeholder="Egyéb étrend">

          <label>Étkezési allergia:</label>
          <input type="text" name="allergy[]" placeholder="Pl. dió, mogyoró...">
        </fieldset>
      </template>
    </div>
  </div>

  <script>
    mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';
    const map = new mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [18.9772, 47.8953],
      zoom: 15
    });
    new mapboxgl.Marker().setLngLat([18.9772, 47.8953]).addTo(map);
  </script>

  <script>
    function addParticipant() {
      const tmpl = document.getElementById("participant-template");
      const clone = tmpl.content.cloneNode(true);
      document.getElementById("participants-container").appendChild(clone);
    }

    function removeParticipant(btn) {
      btn.closest("fieldset").remove();
    }

    function handleSubmit(event) {
      event.preventDefault();

      const guests = [];
      const participants = document.querySelectorAll("#participants-container fieldset");

      participants.forEach(part => {
        const name = part.querySelector('input[name="name[]"]').value.trim();
        const allergy = part.querySelector('input[name="allergy[]"]').value.trim();
        const other = part.querySelector('input[name="diet_other[]"]').value.trim();

        const diets = [];
        part.querySelectorAll('input[type="checkbox"]').forEach(cb => {
          if (cb.checked) diets.push(cb.value);
        });
        if (other) diets.push(other);

        guests.push({ név: name, diéták: diets, alergia: allergy });
      });

      fetch("index.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ guests })
      })
      .then(res => res.text())
      .then(html => {
        document.open(); document.write(html); document.close();
      });
    }

    window.onload = addParticipant;
  </script>
</body>
</html>
