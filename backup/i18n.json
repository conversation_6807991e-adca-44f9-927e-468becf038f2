{"hu": {"title": "Flóra & Ádám", "venue": "<PERSON><PERSON><PERSON>lya, Királyréti Vadászkastély", "date": "2025.09.13.", "welcome": "<PERSON><PERSON>, hogy most már ör<PERSON><PERSON> bírni fogjuk egymást.", "welcome2": "Gyertek velünk <PERSON>, <PERSON><PERSON><PERSON><PERSON>, és tortá<PERSON> enni!", "schedule": "Program", "arrival": "Vendégvárás", "ceremony": "Ceremónia", "photos": "Fotózás", "dinner": "Vacsora", "cake": "<PERSON><PERSON>", "midnight": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "moshpit": "<PERSON><PERSON><PERSON> hajn<PERSON>", "address": "Cím", "accommodation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accommodation_note": "100 főig tudunk szállást biztosítani. <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy jelezd fel<PERSON>, amennyiben ma<PERSON>nál <PERSON>!", "parking": "Parkolás", "parking_note": "A kastély parkolója véges, az út túloldalán lévő parkolóban, illetve a szemben lévő Turista Szálló parkolójában is ingyenesen parkolhattok!", "money_note": "A lakásárak <PERSON>, de a szerelmünk változatlan. Ha nem turmixgépet hozol hanem bor<PERSON>, abba meg belefér egy négyzetméter!", "facebook_group": "Facebook csoport: ", "rsvp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jelezz viss<PERSON>!", "guest": "Vendég", "full_name": "Résztvevő teljes neve", "diet_question": "Követsz-e valamilyen speciális étrendet?", "diet_lactose": "Laktózmentes", "diet_gluten": "Gluténmentes", "diet_sugar": "Cukormentes", "diet_vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diet_vegan": "Vegán", "diet_other": "Egyéb:", "allergy_question": "Van-e ételallergiád?", "add_guest": "Vendég ho<PERSON>a", "high_chair": "Szükségem van etetőszékre.", "contact_info": "Add meg az email c<PERSON><PERSON><PERSON>, melyen a későbbiekben elérünk!", "accommodation_request": "Szeretnél-e szállást kérni? (A szállás korl<PERSON>, és nem biztos, hogy 2 fős szobák állnak rendelkezésre.)", "special_conditions": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> egy<PERSON>b speci<PERSON><PERSON>, am<PERSON><PERSON><PERSON> font<PERSON> tudnunk?", "submit": "<PERSON><PERSON><PERSON><PERSON>", "thank_you": "Köszönjük a v<PERSON>zajelzést! Nagyon várjuk, hogy együtt ünnepelhessünk!", "submit_error": "A beküldés sikertelen volt. Kérlek, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "en": {"title": "Flóra & Ádám", "venue": "<PERSON><PERSON><PERSON>lya, Királyréti Vadászkastély", "date": "2025.09.13.", "welcome": "We've decided that from now on, we'll put up with each other forever.", "welcome2": "Come laugh, cry, and eat cake with us!", "schedule": "Schedule", "arrival": "Guest arrival", "ceremony": "Ceremony", "photos": "Photos", "dinner": "Dinner", "cake": "Cake", "midnight": "Midnight snack", "moshpit": "Moshpit till dawn", "address": "Address", "accommodation": "Accommodation", "accommodation_note": "We can provide accommodation for up to 100 guests. Please let us know in advance if you'd like to stay overnight!", "parking": "Parking", "parking_note": "Parking space at the venue is limited. You can also park for free in the lot across the road or at the Turista Szálló (Tourist Lodge) parking area just opposite.", "money_note": "Housing prices have gone through the roof, but our love is rock solid. If you bring a little envelope instead of a blender, it might just cover a square meter!", "facebook_group": "Facebook group: ", "rsvp": "RSVP", "guest": "Guest", "full_name": "Full name of attendee", "diet_question": "Do you follow any specific diet?", "diet_lactose": "Lactose-free", "diet_gluten": "Gluten-free", "diet_sugar": "Sugar-free", "diet_vegetarian": "Vegetarian", "diet_vegan": "Vegan", "diet_other": "Other:", "allergy_question": "Do you have any food allergies?", "add_guest": "Add Guest", "high_chair": "I need a child chair.", "contact_info": "Your email address so we can reach you later:", "accommodation_request": "Would you like to request accommodation? (Note: limited availability, and not all rooms are double occupancy.)", "special_conditions": "Is there anything else we should know about your needs?", "submit": "Submit", "thank_you": "Thank you for confirming your attendance. We can't wait to celebrate with you!", "submit_error": "Submission failed. Please try again."}}