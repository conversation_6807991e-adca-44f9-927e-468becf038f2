<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Wedding Invite</title>
  <style>
    body {
      margin: 0;
      background-color: #e6f4e6; /* Light green */
      font-family: Arial, sans-serif;
      overflow-x: hidden;
    }

    .container {
      position: relative;
      max-width: 800px;
      margin: auto;
    }

    .layer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      transition: opacity 1s ease;
    }

    .layer img {
      width: 100%;
      display: block;
    }

    #layer1 {
      position: sticky;
      top: 0;
      width: 100%;
      z-index: 1;
      opacity: 1;
    }

    #layer2 {
      z-index: 2;
      opacity: 0;
      pointer-events: none;
    }

    .text-over {
      position: relative;
    }

    .text-over span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 2rem;
      text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.6);
      pointer-events: none;
    }
  </style>
</head>
<body>

  <div class="spacer"></div>

  <div class="container" id="container">
    <div class="layer" id="layer1">
      xxxx
      <img src="invite1.png" alt="Invite 1" />
    </div>
    <div class="layer" id="layer2">
      <img src="invite1.png" alt="Invite 1 again" />
      <div class="text-over">
        <img src="invite2.png" alt="Invite 2" />
        <span>Lorem Ipsum</span>
      </div>
    </div>
  </div>

  <div class="spacer"></div>

  <script>
    const layer1 = document.getElementById("layer1");
    const layer2 = document.getElementById("layer2");

    window.addEventListener("scroll", () => {
      const scrollY = window.scrollY;
      const fadeStart = 800;
      const fadeEnd = 900;

      let opacity = (scrollY - fadeStart) / (fadeEnd - fadeStart);
      opacity = Math.min(1, Math.max(0, opacity));

      layer1.style.opacity = 1 - opacity;
      layer2.style.opacity = opacity;
    });
  </script>
</body>
</html>
