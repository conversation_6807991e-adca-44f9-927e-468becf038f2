<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Flóra & Ádám Esküvő Meghívó</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=EB+Garamond&display=swap" rel="stylesheet">
  <link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
  <script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: 'E<PERSON> Garamond', serif;
      background: linear-gradient(135deg, #dfe9c4 0%, #cce0b1 50%, #e3e9cf 100%);
      color: #2f2f2f;
      overflow: hidden;
    }

    .scroll-container {
      height: 100vh;
      overflow-y: scroll;
      border: 0;
      margin: 0;
      padding: 0;
    }

    .content-wrapper {
      position: relative;
      padding: 0;
      width: 90vh;
      max-width: 900px;
      margin: 0 auto 4rem auto;
      border: 0;
      border-radius: 10px;
    }

    .sticky-header {
      position: sticky;
      top: 0;
      width: 100%;
      max-width: 100%;
      height: auto;
      background: yellow;
      z-index: 1;
      margin: 0;
      padding: 0;
    }

    .sticky-header img {
      width: 100%;
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      transition: opacity 0.1s linear;
    }

    .sticky-img-1 {
      z-index: 2;
      opacity: 1;
    }

    .sticky-img-2 {
      z-index: 1;
      opacity: 1;
    }

    .content-image {
      width: 90vh;
      max-width: 100%;
      height: auto;
      margin-top: 0;
      background:
        url('invite-top.png') no-repeat top center,
        url('invite-middle.png') repeat-y center,
        url('invite-bottom.png') no-repeat bottom center;
      background-size: 100%, contain, 100%;
      position: relative;
      height: 900px;
    }

    .content-image img {
      width: 100%;
      display: block;
      /*z-index: -999;*/
    }
    
    .text-container {
      position: absolute;
      top: 0;
      bottom: 0;
      width: auto;
      margin-left: auto;
      margin-right: auto;
      padding-left: 5rem;
      padding-right: 5rem;
      z-index: 900;
    }

    .hidden {
      display: none;
    }

    .container-inner {
      padding: 4rem 2rem;
    }

    h2 {
      font-size: 2rem;
      text-align: center;
      margin-top: 0;
      font-style: italic;
    }

    p {
      text-align: center;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      font-style: italic;
    }

    .section {
      margin-bottom: 2.5rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0 auto;
      max-width: 300px;
    }

    ul li {
      text-align: center;
      margin: 1rem;
    }

    .gps {
      font-size: 0.9em;
      color: #555;
    }

    #map {
      width: 100%;
      height: 300px;
      border-radius: 8px;
      margin: 1rem 0;
    }

    .map-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 1rem;
    }

    .map-buttons a {
      background-color: #789262;
      color: white;
      padding: 0.6rem 1.2rem;
      border-radius: 6px;
      text-decoration: none;
      font-weight: bold;
      transition: background-color 0.3s;
    }

    .map-buttons a:hover {
      background-color: #5e734a;
    }

    .footer {
      font-style: italic;
      text-align: center;
      margin-top: 2rem;
      font-size: 1.5rem;
    }

    .highlight {
      font-weight: bold;
      font-size: 1.1rem;
    }

    form label {
      display: block;
      margin: 0.5rem 0 0.2rem;
    }

    form input[type="text"],
    form input[type="email"],
    form textarea {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: 'EB Garamond', serif;
      font-size: 1rem;
      margin-bottom: 1rem;
      box-sizing: border-box;
    }

    .participant {
      border: 1px solid #a6b98b;
      background: #f9f9f9;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .participant legend {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .checkbox-group label {
      display: inline-block;
      margin-right: 1rem;
      font-size: 0.95rem;
    }

    .actions {
      text-align: center;
      margin-top: 1.5rem;
    }

    .actions button {
      padding: 0.6rem 1.5rem;
      background-color: #789262;
      color: white;
      border: none;
      border-radius: 6px;
      font-weight: bold;
      font-size: 1rem;
      margin: 0.5rem;
      cursor: pointer;
    }

    .actions button:hover {
      background-color: #5e734a;
    }

    .remove-btn {
      float: right;
      background-color: #b15353;
      border: none;
      color: white;
      font-size: 0.8rem;
      padding: 0.2rem 0.6rem;
      border-radius: 4px;
      cursor: pointer;
    }

    .remove-btn:hover {
      background-color: #933;
    }
  </style>
</head>
<body>
  <div class="scroll-container" id="scrollContainer">
    <div class="content-wrapper">
      <div class="sticky-header" id="stickyHeader">
        <img src="invite1.jpg" alt="Invite 1" class="sticky-img-1" id="img1" />
        <img src="invite2.png" alt="Invite 2" class="sticky-img-2" id="img2" />
      </div>
        <p style="margin-top: 0">&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
        <p>&nbsp;</p>
      <div class="content-image" id="contentImage">
        <!--<img src="invite-top.png" alt="Invite 2" />
        <img src="invite-middle.png" alt="Invite 2" />
        <img src="invite-middle.png" alt="Invite 2" />
        <img src="invite-middle.png" alt="Invite 2" />
        <img src="invite-middle.png" alt="Invite 2" />
        <img src="invite-middle.png" alt="Invite 2" />
        
        <img src="invite-bottom.png" alt="Invite 2" />-->
               <!-- Dummy content -->
        <div class="text-container">

        <p>&nbsp;</p>
        <p>&nbsp;</p>
            <div class="container-inner">
      <p>Úgy döntöttünk, hogy most már örökre bírni fogjuk egymást.<br>
      Gyertek velünk nevetni, sírni, és tortát enni!</p>

      <div class="section">
        <h2>Program</h2>
        <ul>
          <li>14:30 – vendégvárás</li>
          <li>15:30 – ceremónia</li>
          <li>16:00 – fotózás</li>
          <li>19:00 – vacsora</li>
          <li>23:00 – torta</li>
          <li>0:00 – éjféli vacsora</li>
          <li>0:45 – moshpit hajnalig</li>
        </ul>
      </div>

      <div class="section">
        <h2>Cím</h2>
        <p>Szokolya, Királyrét, 2624<br>
          <span class="gps">47.89533180751165, 18.97720805857283</span>
        </p>
        <div id="map"></div>
        <div class="map-buttons">
          <a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank">📍 Google Térkép</a>
          <a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes" target="_blank">🚗 Waze útvonal</a>
        </div>
      </div>

      <p>A lakásárak elszálltak, de a szerelmünk változatlan. Ha nem turmixgépet hozol hanem borítékot, abba meg belefér egy négyzetméter!
  </p>

      <div class="footer">
        <span class="highlight">Facebook csoport:</span> Flóra & Ádám házasodik
      </div>

        </div>
  </div>

      <div class="section">
        <h2>RSVP – Visszajelzés</h2>
        <form id="rsvp-form" onsubmit="handleSubmit(event)">
          <div id="participants-container"></div>
          <div class="actions">
            <button type="button" onclick="addParticipant()">Vendég hozzáadása</button>
            <button type="submit">Beküldés</button>
          </div>
        </form>
      </div>

      <template id="participant-template">
        <fieldset class="participant">
          <legend>Vendég <button type="button" class="remove-btn" onclick="removeParticipant(this)">mégse</button></legend>
          <label>Teljes név:</label>
          <input type="text" name="name[]" required>

          <label>Speciális étrend:</label>
          <div class="checkbox-group">
            <label><input type="checkbox" value="laktózmentes"> Laktózmentes</label>
            <label><input type="checkbox" value="gluténmentes"> Gluténmentes</label>
            <label><input type="checkbox" value="cukormentes"> Cukormentes</label>
            <label><input type="checkbox" value="vegetáriánus"> Vegetáriánus</label>
            <label><input type="checkbox" value="vegán"> Vegán</label>
          </div>
          <label>Egyéb:</label>
          <input type="text" name="diet_other[]" placeholder="Egyéb étrend">

          <label>Étkezési allergia:</label>
          <input type="text" name="allergy[]" placeholder="Pl. dió, mogyoró...">
        </fieldset>
      </template>
    </div>

      
      <div>
 
      </div>
    </div>
  </div>

  <script>
    const scrollContainer = document.getElementById('scrollContainer');
    const stickyHeader = document.getElementById('stickyHeader');
    const contentImage = document.getElementById('contentImage');
    const img1 = document.getElementById('img1');
    const img2 = document.getElementById('img2');
    const imgHeight = document.getElementById('img1').height;

    scrollContainer.addEventListener('scroll', () => {
      const containerRect = scrollContainer.getBoundingClientRect();
      const contentRect = contentImage.getBoundingClientRect();
      const stickyRect = stickyHeader.getBoundingClientRect();

      const distance = contentRect.top - stickyRect.top;
      const fadeStart = 250;
      const fadeEnd = 700;

      if (distance <= 0) {
        stickyHeader.classList.add('hidden');
      } else {
        stickyHeader.classList.remove('hidden');

        // Compute fade progress
        let progress = 1 - Math.max(0, Math.min(1, distance / fadeEnd));
        img1.style.opacity = 1 - progress;
        //img2.style.opacity = progress;
      }
    });
  </script>
    <script>
    mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';
    const map = new mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [18.9772, 47.8953],
      zoom: 15
    });
    new mapboxgl.Marker().setLngLat([18.9772, 47.8953]).addTo(map);
  </script>

  <script>
    function addParticipant() {
      const tmpl = document.getElementById("participant-template");
      const clone = tmpl.content.cloneNode(true);
      document.getElementById("participants-container").appendChild(clone);
    }

    function removeParticipant(btn) {
      btn.closest("fieldset").remove();
    }

    function handleSubmit(event) {
      event.preventDefault();

      const guests = [];
      const participants = document.querySelectorAll("#participants-container fieldset");

      participants.forEach(part => {
        const name = part.querySelector('input[name="name[]"]').value.trim();
        const allergy = part.querySelector('input[name="allergy[]"]').value.trim();
        const other = part.querySelector('input[name="diet_other[]"]').value.trim();

        const diets = [];
        part.querySelectorAll('input[type="checkbox"]').forEach(cb => {
          if (cb.checked) diets.push(cb.value);
        });
        if (other) diets.push(other);

        guests.push({ név: name, diéták: diets, alergia: allergy });
      });

      fetch("index.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ guests })
      })
      .then(res => res.text())
      .then(html => {
        document.open(); document.write(html); document.close();
      });
    }

    window.onload = addParticipant;
  </script>
</body>
</html>
