<!DOCTYPE html>
<html lang="hu">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RSVP Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.3.0/dist/flowbite.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.3.0/dist/flowbite.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.3.0/dist/flowbite.plugin.min.js"></script>
</head>
<body class="bg-gray-100 text-gray-900 p-6">
  <h1 class="text-2xl font-bold mb-6">RSVP Beküldések</h1>

  <?php
  ini_set('display_errors', 1);
  ini_set('display_startup_errors', 1);
  error_reporting(E_ALL);

  $db = new PDO('sqlite:rsvp.sqlite');
  $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  $rows = $db->query("SELECT r.*, g.* FROM rsvps r JOIN guests g ON g.submission_id = r.id ORDER BY r.submitted_at DESC, g.id ASC")
             ->fetchAll(PDO::FETCH_ASSOC);

  // Count guests per submission_id
  $groupCounts = [];
  foreach ($rows as $row) {
    $sid = $row['submission_id'];
    if (!isset($groupCounts[$sid])) $groupCounts[$sid] = 0;
    $groupCounts[$sid]++;
  }
  ?>

  <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <table id="rsvp-table" class="w-full text-sm text-left text-gray-700">
      <thead class="text-xs text-gray-700 uppercase bg-gray-100">
        <tr>
          <th scope="col" class="px-4 py-3">Vendég neve</th>
          <th scope="col" class="px-4 py-3">Diéta</th>
          <th scope="col" class="px-4 py-3">Allergia</th>
          <th scope="col" class="px-4 py-3">Kapcsolat</th>
          <th scope="col" class="px-4 py-3">Szállás</th>
          <th scope="col" class="px-4 py-3">Kérés</th>
          <th scope="col" class="px-4 py-3">Gyerek szék</th>
          <th scope="col" class="px-4 py-3">Beküldve</th>
        </tr>
      </thead>
      <tbody>
        <?php
        $rowColors = ['bg-white', 'bg-gray-50'];
        $colorMap = [];
        $colorIndex = 0;
        $printed = [];

        foreach ($rows as $row):
          $submissionId = $row['submission_id'];
          if (!isset($colorMap[$submissionId])) {
            $colorMap[$submissionId] = $rowColors[$colorIndex % 2];
            $colorIndex++;
          }
          $bgClass = $colorMap[$submissionId];

          $diets = [];
          foreach (["lactose", "gluten", "sugar", "vegetarian", "vegan"] as $diet) {
            if (!empty($row["diet_{$diet}"])) $diets[] = $diet;
          }
          if (!empty($row['special_diet'])) {
            $diets[] = htmlspecialchars($row['special_diet']);
          }
        ?>
          <tr class="border-b <?php echo $bgClass; ?>">
            <td class="px-4 py-2"><?php echo htmlspecialchars($row['name']); ?></td>
            <td class="px-4 py-2"><?php echo $diets ? implode(', ', $diets) : '-'; ?></td>
            <td class="px-4 py-2"><?php echo htmlspecialchars($row['allergy']) ?: '-'; ?></td>

            <?php if (!isset($printed[$submissionId])): ?>
              <td class="px-4 py-2 align-top" rowspan="<?php echo $groupCounts[$submissionId]; ?>"><?php echo htmlspecialchars($row['contact']); ?></td>
              <td class="px-4 py-2 align-top" rowspan="<?php echo $groupCounts[$submissionId]; ?>"><?php echo htmlspecialchars($row['accomodation']); ?></td>
              <td class="px-4 py-2 align-top" rowspan="<?php echo $groupCounts[$submissionId]; ?>"><?php echo htmlspecialchars($row['special_request']); ?></td>
              <td class="px-4 py-2 align-top text-center" rowspan="<?php echo $groupCounts[$submissionId]; ?>"><?php echo $row['child_chair'] ? '🧒 Igen' : 'Nem'; ?></td>
              <td class="px-4 py-2 align-top text-sm text-gray-500" rowspan="<?php echo $groupCounts[$submissionId]; ?>"><?php echo $row['submitted_at']; ?></td>
              <?php $printed[$submissionId] = true; ?>
            <?php endif; ?>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>
  </div>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      Flowbite.initDatatables();
    });
  </script>
</body>
</html>
