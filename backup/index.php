<!DOCTYPE html>
<html lang="hu" x-data="app()" x-init="init()">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Flóra & Ádám Meghívó</title>

    <!-- Open Graph / Facebook Metadata -->
    <meta property="og:title" content="Flóra & Ádám Esküvő" />
    <meta property="og:description" content="Szeretettel meghívunk az esküvőnkre! Itt tudsz visszajelezni." />
    <meta property="og:image" content="https://www.fladam.hu/favico.png" />
    <meta property="og:url" content="https://www.fladam.hu/" />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="hu_HU" />

    <!-- Optional Twitter Card (if needed) -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Flóra & Ádám Meghívó" />
    <meta name="twitter:description" content="Szeretettel meghívunk az esküvőnkre!" />
    <meta name="twitter:image" content="https://www.fladam.hu/favico.png" />

    <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=EB+Garamond&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>
    <style>
      body {
        font-family: 'EB Garamond', serif;
      }
      h1 {
        font-family: 'Great Vibes', cursive;
      }
      .titlebox {
          margin-top: 140px;
          padding-left: 40px;
          margin-bottom: 150px;
        }

        /* Mobile styles */
        @media (max-width: 767px) {
          .titlebox {
            margin-top: 40px;
            padding-left: 0; /* or remove the line */
            margin-bottom: 100px;
          }
        }
      p, legend, label, li, button {
        font-size: 1.23em;
      }
      h2 {
        text-decoration: underline;
        text-decoration-color: rgb(32, 155, 79);
        text-decoration-thickness: 1px;
      }
      .styled-checkbox {
        display: inline-flex;
        align-items: center;
        white-space: nowrap;
        gap: 8px; /* or use margin-right on checkbox */
      }

      .styled-checkbox input[type="checkbox"] {
        appearance: none;
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        border: 2px solid #ccc;
        border-radius: 4px;
        position: relative;
        cursor: pointer;
      }

      .styled-checkbox input[type="checkbox"]:checked {
        background-color: #4CAF50;
        border-color: #4CAF50;
      }

      .styled-checkbox input[type="checkbox"]:checked::after {
        content: "✔";
        color: white;
        position: absolute;
        left: 1px;
        top: -4px;
        font-size: 18px;
      }

      .styled-checkbox span {
        cursor: pointer !important;
      }


      .removeguestbutton {

      }

      .guestfieldset {
        position: relative;
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-lime-50 to-emerald-50 text-gray-800">
    <div class="max-w-4xl mx-auto my-10 p-10 bg-white/80 border-double border-8 border-green-800 rounded-lg shadow-xl"
      style="position: relative; background-image: url('invite-bg-faded.png'); background-repeat: no-repeat; background-position: top 100px; background-size: contain"
      x-show="!success" x-transition:leave.duration.500ms
    >
      <!-- Language toggle -->
      <div style="position: absolute; top: 5px; right: 5px" class="flex justify-end mb-4 gap-2">
        <button @click="lang = 'hu'" :class="lang === 'hu' ? 'bg-green-700 text-white' : 'border'" class="font-sans px-4 py-1 rounded hover:bg-green-100">
          🇭🇺 HU
        </button>
        <button @click="lang = 'en'" :class="lang === 'en' ? 'bg-green-700 text-white' : 'border'" class="font-sans px-4 py-1 rounded hover:bg-green-100">
          🇬🇧 EN
        </button>
      </div>

      <div>
        <div class="titlebox">
          <h1 class="text-5xl text-center mb-2">Flóra & Ádám</h1>
          <h1 class="text-4xl text-center mb-2">2025. 09. 13.</h1>
        </div>

        <h1 class="text-4xl text-center mb-2">Szokolya,</h1>
        <h1 class="text-4xl text-center mb-10">Királyréti Vadászkastély</h1>
        <p class="text-center mb-0" x-text="t.welcome"></p>
        <p class="text-center mb-10" x-text="t.welcome2"></p>

        <section class="mb-10">
          <h2 class="text-center text-xl mb-2" x-text="t.schedule"></h2>
          <ul class="space-y-1 text-center">
            <li><span>14:30 – </span><span x-text="t.arrival"></span></li>
            <li><span>15:30 – </span><span x-text="t.ceremony"></span></li>
            <li><span>16:00 – </span><span x-text="t.photos"></span></li>
            <li><span>19:00 – </span><span x-text="t.dinner"></span></li>
            <li><span>23:00 – </span><span x-text="t.cake"></span></li>
            <li><span>0:00 – </span><span x-text="t.midnight"></span></li>
            <li><span>0:45 – </span><span x-text="t.moshpit"></span></li>
          </ul>
        </section>

        <section class="mb-10">
          <h2 class="text-center text-xl mb-2" x-text="t.address"></h2>
          <p class="text-center mb-4">
            Szokolya, Királyrét, 2624<br />
            <span class="text-sm text-gray-600">47.89533180751165, 18.97720805857283</span>
          </p>
          <div id="map" class="w-full h-64 rounded-lg"></div>

          <div class="flex justify-center gap-4 mt-4">
            <a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank" class="font-sans bg-green-700 text-white px-6 py-3.5 font-medium rounded hover:bg-green-900 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C8.1 2 5 5.1 5 9c0 5.2 7 13 7 13s7-7.8 7-13c0-3.9-3.1-7-7-7zm0 9.5c-1.4 0-2.5-1.1-2.5-2.5S10.6 6.5 12 6.5s2.5 1.1 2.5 2.5S13.4 11.5 12 11.5z"/>
              </svg>
              Google Maps
            </a>
            <a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes&q=Királyréti%20Vadászkastély" target="_blank" class="font-sans bg-[#33CCFF] text-white px-6 py-3.5 font-medium rounded hover:bg-[#1aa3cc] flex items-center gap-2">
              <img src="/waze-icon.svg" alt="Waze" class="w-5 h-5">
              Waze navigáció indítása
            </a>
          </div>
        </section>

        <section class="mb-10">
          <h2 class="text-center text-xl mb-2" x-text="t.accommodation"></h2>
          <p class="text-center" x-text="t.accommodation_note"></p>
        </section>

        <section class="mb-10">
          <h2 class="text-center text-xl mb-2" x-text="t.parking"></h2>
          <p class="text-center" x-text="t.parking_note"></p>
        </section>

        <p class="italic text-center mb-4" x-text="t.money_note"></p>
        <hr />

        <section class="mb-10 mt-10 text-center">
          <a href="https://www.facebook.com/groups/1825765404652189"
             class="font-sans inline-flex justify-center items-center py-3 px-5 text-base font-medium text-center text-white rounded-lg bg-[#1877F2] hover:bg-[#166fe5]">
            <img src="facebook-icon.svg" alt="Waze" class="w-5 h-5 mr-5">
             <span x-text="t.facebook_group"></span>&nbsp;<span>Flóra&Ádám házasodik</span>
          </a>
        </section>

        <hr />

        <section class="mt-12">
          <h2 class="text-center text-3xl mb-4" x-text="t.rsvp"></h2>
          <form class="space-y-6" @submit.prevent="submitForm">

            <template x-for="(guest, index) in guests" :key="index">
              <fieldset class="border p-4 rounded" class="guestfieldset">
                <legend class="font-semibold mb-0" x-text="t.guest + ' #' + (index + 1)"></legend>
                <label class="font-semibold mt-0" x-text="t.full_name"></label>
                <input type="text" class="w-full border p-2 rounded mb-2 focus:outline-none focus:ring-0 focus:border-green-800" x-model="guest.name" required />
                <div class="mb-2">
                  <p class="font-semibold" x-text="t.diet_question"></p>
                  <div class="space-y-1">
                    <template x-for="opt in ['lactose','gluten','sugar','vegetarian','vegan']">
                      <label class="styled-checkbox mx-1 text-base">
                        <input type="checkbox" :value="opt" x-model="guest.diet" class="mr-0"><span x-text="t['diet_' + opt]"></span>
                      </label>
                    </template>
                    <label class="text-base"><span x-text="t.diet_other"></span>
                    <input type="text" x-model="guest.diet_other" class="ml-2 border p-1 rounded text-base focus:outline-none focus:ring-0 focus:border-green-800" style="width: 100px" />
                  </label>
                  </div>
                </div>
                <div class="mb-2">
                  <label class="font-semibold" x-text="t.allergy_question"></label>
                  <input type="text" x-model="guest.allergy" class="w-full border p-2 rounded focus:outline-none focus:ring-0 focus:border-green-800" />
                </div>
                <div class="items-right justify-right text-right">
                <button type="button" @click="guests.splice(index, 1)" x-show="index > 0"
                        class="removeguestbutton mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-800">
                  ✕ <span x-text="t.remove_guest || 'Remove guest'"></span>
                </button>
              </div>
              </fieldset>
            </template>

            <div class="flex gap-4">
              <button type="button" @click="guests.push({name:'',diet:[],diet_other:'',allergy:''})"
                      class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-800" x-text="t.add_guest"></button>
            </div>

            <div class="mt-6 space-y-4">
              <div>
                <label class="styled-checkbox"><input type="checkbox" x-model="high_chair" class="mr-2"><span x-text="t.high_chair"></span></label>
              </div>
              <div>
                <label class="block font-semibold" x-text="t.contact_info"></label>
                <input type="email" x-model="contact" class="w-full border p-2 rounded focus:outline-none focus:ring-0 focus:border-green-800" required />
              </div>
              <div>
                <label class="block font-semibold" x-text="t.accommodation_request"></label>
                <input type="text" x-model="accommodation" class="w-full border p-2 rounded focus:outline-none focus:ring-0 focus:border-green-800" />
              </div>
              <div>
                <label class="block font-semibold" x-text="t.special_conditions"></label>
                <textarea x-model="special_notes" class="w-full border p-2 rounded focus:outline-none focus:ring-0 focus:border-green-800"></textarea>
              </div>
              <div class="text-center mt-6">
                <button
                  type="submit"
                  class="bg-green-700 text-white px-6 py-2 rounded hover:bg-green-900 disabled:opacity-50"
                  :disabled="isSubmitting"
                  x-text="isSubmitting ? (lang === 'hu' ? 'Kérlek várj...' : 'Please wait...') : t.submit"
                ></button>
              </div>
            </div>
          </form>
          <div class="text-red-600 text-center mt-2" x-text="errorMessage" x-show="errorMessage"></div>
        </section>
      </div>
    </div>

    <section class="mt-12 text-center text-xl text-green-800 font-semibold"
         x-show="success"
         x-transition:enter="transition ease-out duration-700"
         x-transition:enter-start="opacity-0 translate-y-4"
         x-transition:enter-end="opacity-100 translate-y-0">
          <p style="margin-top: 120px; margin-bottom: 190px" x-text="t.thank_you || 'Köszönjük a visszajelzést! Nagyon várjuk, hogy együtt ünnepelhessünk!'"></p>
        </section>

    <script>
      const app = () => ({
        lang: 'hu',
        t: {},
        translations: {},
        guests: [{ name: '', diet: [], diet_other: '', allergy: '' }],
        high_chair: false,
        contact: '',
        accommodation: '',
        special_notes: '',
        success: false,
        errorMessage: '',
        isSubmitting: false,
        init() {
          const browserLang = navigator.language.startsWith('en') ? 'en' : 'hu';
          this.lang = browserLang;

          fetch('i18n.json')
            .then(res => res.json())
            .then(data => {
              this.translations = data;
              this.t = data[this.lang];
              this.$watch('lang', val => this.t = this.translations[val]);
            });

          mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';
          const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [18.97720805857283, 47.89533180751165],
            zoom: 15
          });
          new mapboxgl.Marker()
            .setLngLat([18.97720805857283, 47.89533180751165])
            .setPopup(new mapboxgl.Popup().setHTML('<strong>Királyréti Vadászkastély</strong>'))
            .addTo(map);
        },
        submitForm() {
          this.isSubmitting = true;
          const guestData = this.guests.map(g => ({
            name: g.name,
            diet_lactose: g.diet.includes('lactose'),
            diet_gluten: g.diet.includes('gluten'),
            diet_sugar: g.diet.includes('sugar'),
            diet_vegetarian: g.diet.includes('vegetarian'),
            diet_vegan: g.diet.includes('vegan'),
            special_diet: g.diet_other,
            allergy: g.allergy
          }));

          const payload = {
            guests: guestData,
            contact: this.contact,
            accomodation: this.accommodation,
            special_request: this.special_notes,
            child_chair: this.high_chair,
            lang: this.lang
          };

          this.errorMessage = '';

          fetch('submit.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          })
            .then(res => res.json())
            .then(data => {
              //this.isSubmitting = false;
              if (data.success) {
                this.success = true;
              } else {
                this.errorMessage = data.error || this.t.submit_error || 'Hiba történt a küldés során.';
              }
            })
            .catch(() => {
              this.isSubmitting = false;
              this.errorMessage = this.t.submit_error || 'Hiba történt a küldés során.';
            });
        }

      });
    </script>
  </body>
</html>
