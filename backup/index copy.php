<!DOCTYPE html>
<html lang="hu" x-data="app()" x-init="init()">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Flóra & Ádám <PERSON>ívó</title>
    <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=EB+Garamond&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>
    <style>
      body {
        font-family: '<PERSON><PERSON> Garamond', serif;
      }
      h1 {
        font-family: 'Great Vibes', cursive;
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-lime-50 via-lime-100 to-lime-50 text-gray-800">
    <div class="max-w-4xl mx-auto my-10 p-10 bg-white/80 border-double border-8 border-green-800 rounded-lg shadow-xl">
      <div class="flex justify-end mb-4 gap-2">
        <button @click="lang = 'hu'" :class="lang === 'hu' ? 'bg-green-700 text-white' : 'border'" class="px-4 py-1 rounded hover:bg-green-100">HU</button>
        <button @click="lang = 'en'" :class="lang === 'en' ? 'bg-green-700 text-white' : 'border'" class="px-4 py-1 rounded hover:bg-green-100">EN</button>
      </div>

      <h1 class="text-5xl text-center mb-2" x-text="t.title"></h1>
      <h2 class="text-center text-lg mb-6" x-text="t.venue + ' – ' + t.date"></h2>
      <p class="text-center mb-10" x-text="t.welcome"></p>

      <section class="mb-10">
        <h2 class="text-center text-xl mb-2" x-text="t.schedule"></h2>
        <ul class="space-y-1 text-center">
          <li><span>14:30 – </span><span x-text="t.arrival"></span></li>
          <li><span>15:30 – </span><span x-text="t.ceremony"></span></li>
          <li><span>16:00 – </span><span x-text="t.photos"></span></li>
          <li><span>19:00 – </span><span x-text="t.dinner"></span></li>
          <li><span>23:00 – </span><span x-text="t.cake"></span></li>
          <li><span>0:00 – </span><span x-text="t.midnight"></span></li>
          <li><span>0:45 – </span><span x-text="t.moshpit"></span></li>
        </ul>
      </section>

      <section class="mb-10">
        <h2 class="text-center text-xl mb-2" x-text="t.address"></h2>
        <p class="text-center mb-4">
          Szokolya, Királyrét, 2624<br />
          <span class="text-sm text-gray-600">47.89533180751165, 18.97720805857283</span>
        </p>
        <div id="map" class="w-full h-64 rounded-lg"></div>
        <div class="flex justify-center gap-4 mt-4">
          <a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank" class="bg-green-700 text-white px-4 py-2 rounded hover:bg-green-900">Google Maps</a>
          <a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes" target="_blank" class="bg-[#33CCFF] text-white px-4 py-2 rounded hover:bg-[#1aa3cc]">Waze</a>
        </div>
      </section>

      <section class="mb-10">
        <h2 class="text-center text-xl mb-2" x-text="t.accommodation"></h2>
        <p class="text-center" x-text="t.accommodation_note"></p>
      </section>

      <section class="mb-10">
        <h2 class="text-center text-xl mb-2" x-text="t.parking"></h2>
        <p class="text-center" x-text="t.parking_note"></p>
      </section>

      <p class="italic text-center mb-4" x-text="t.money_note"></p>
      <p class="text-center font-semibold" x-text="t.facebook_group"></p>

      <section class="mt-12">
        <h2 class="text-center text-xl mb-4" x-text="t.rsvp"></h2>
        <form class="space-y-6" @submit.prevent="submitForm">
          <template x-for="(guest, index) in guests" :key="index">
            <fieldset class="border p-4 rounded">
              <legend class="font-semibold mb-2" x-text="t.full_name + ' #' + (index + 1)"></legend>
              <input type="text" class="w-full border p-2 rounded mb-2" x-model="guest.name" required />
              <div class="mb-2">
                <p class="font-semibold" x-text="t.diet_question"></p>
                <div class="space-y-1">
                  <template x-for="opt in ['lactose','gluten','sugar','vegetarian','vegan']">
                    <label class="mx-2">
                      <input type="checkbox" :value="opt" x-model="guest.diet" class="mr-2"><span x-text="t['diet_' + opt]"></span>
                    </label>
                  </template>
                  <label><span x-text="t.diet_other"></span> <input type="text" x-model="guest.diet_other" class="ml-2 border p-1 rounded" /></label>
                </div>
              </div>
              <div class="mb-2">
                <label class="font-semibold" x-text="t.allergy_question"></label>
                <input type="text" x-model="guest.allergy" class="w-full border p-2 rounded" />
              </div>
            </fieldset>
          </template>

          <div class="flex gap-4">
            <button type="button" @click="guests.push({name:'',diet:[],diet_other:'',allergy:''})"
              class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-800">+ Add Guest</button>
            <button type="button" @click="guests.pop()" x-show="guests.length > 1"
              class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-800">– Remove Last</button>
          </div>

          <div class="mt-6 space-y-4">
            <div>
              <label><input type="checkbox" x-model="high_chair" class="mr-2"><span x-text="t.high_chair"></span></label>
            </div>

            <div>
              <label class="block font-semibold" x-text="t.contact_info"></label>
              <input type="text" x-model="contact" class="w-full border p-2 rounded" />
            </div>

            <div>
              <label class="block font-semibold" x-text="t.accommodation_request"></label>
              <input type="text" x-model="accommodation" class="w-full border p-2 rounded" />
            </div>

            <div>
              <label class="block font-semibold" x-text="t.special_conditions"></label>
              <input type="text" x-model="special_notes" class="w-full border p-2 rounded" />
            </div>

            <div class="text-center mt-6">
              <button type="submit" class="bg-green-700 text-white px-6 py-2 rounded hover:bg-green-900" x-text="t.submit"></button>
            </div>
          </div>
        </form>
      </section>
    </div>

    <script>
      const app = () => ({
        lang: 'hu',
        t: {},
        translations: {},
        guests: [{ name: '', diet: [], diet_other: '', allergy: '' }],
        high_chair: false,
        contact: '',
        accommodation: '',
        special_notes: '',
        init() {
          const browserLang = navigator.language.startsWith('en') ? 'en' : 'hu';
          this.lang = browserLang;

          fetch('i18n.json')
            .then(res => res.json())
            .then(data => {
              this.translations = data;
              this.t = data[this.lang];
              this.$watch('lang', val => this.t = this.translations[val]);
            });

          mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';
          const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [18.97720805857283, 47.89533180751165],
            zoom: 15
          });
          new mapboxgl.Marker()
            .setLngLat([18.97720805857283, 47.89533180751165])
            .setPopup(new mapboxgl.Popup().setHTML('<strong>Királyréti Vadászkastély</strong>'))
            .addTo(map);
        },
        submitForm() {
          console.log('Guests:', this.guests);
          console.log('Contact:', this.contact);
          alert(this.t.submit + '!');
        }
      });
    </script>
  </body>
</html>
