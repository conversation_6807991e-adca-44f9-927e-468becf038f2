<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Simple Green Website</title>
  <style>
    body {
      background-color: #d4edc9;
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    .container {
      max-width: 800px;
      margin: 2rem auto;
      line-height: 3rem;
      padding: 0;
      position: relative;
      background-image: url('front.jpg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center top;
      background-attachment: fixed;
    }

    .overlay-front {
      height: 800px;
      text-align: center;
      padding-top: 300px;
      z-index: 1;
    }

    .overlay-text {
      padding: 2rem;
      text-align: center;
      position: relative;
      z-index: 1;
      transition: background 0.4s ease;
    }

    /* This will be applied when it reaches the top */
    .overlay-text.scrolled {
      background-image: url('back.jpg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center top;
      background-attachment: scroll;
      background-color: rgba(255, 255, 255, 0.85);
    }

    h1, h2, p {
      margin: 1rem 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="overlay-front">
      <h1>Flóra & Ádám</h1>
      <h2>2025</h2>
    </div>
    <div class="overlay-text" id="overlayText">
      <h1>Lorem Ipsum</h1>
      <h2>Dolor Sit Amet</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus lacinia odio vitae vestibulum vestibulum.</p>
      <p>Cras vehicula, mi eget tincidunt suscipit, sapien leo euismod erat, ut sodales justo nulla in erat.</p>
      <h1>Lorem Ipsum</h1>
      <h2>Dolor Sit Amet</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus lacinia odio vitae vestibulum vestibulum.</p>
      <p>Cras vehicula, mi eget tincidunt suscipit, sapien leo euismod erat, ut sodales justo nulla in erat.</p>
      <h1>Lorem Ipsum</h1>
      <h2>Dolor Sit Amet</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus lacinia odio vitae vestibulum vestibulum.</p>
      <p>Cras vehicula, mi eget tincidunt suscipit, sapien leo euismod erat, ut sodales justo nulla in erat.</p>
    </div>
  </div>

  <script>
    const overlayText = document.getElementById('overlayText');

    window.addEventListener('scroll', () => {
      const top = overlayText.getBoundingClientRect().top;
      if (top <= 0) {
        overlayText.classList.add('scrolled');
      } else {
        overlayText.classList.remove('scrolled');
      }
    });
  </script>
</body>
</html>
