<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && str_starts_with($_SERVER['CONTENT_TYPE'], 'application/json')) {
    $data = json_decode(file_get_contents('php://input'), true);
    echo "<pre>";
    echo "Feldolgozott vendégadatok:\n\n";
    print_r($data['guests'] ?? []);
    echo "</pre><hr />";
    exit;
}
?>

<!DOCTYPE html>
<html lang="hu">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Esküvői RSVP</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css" rel="stylesheet" />
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-green-50 min-h-screen p-6 text-gray-800">

  <div class="max-w-5xl mx-auto space-y-10">
    
    <!-- Wedding Info -->
    <div class="bg-green-100 border border-green-300 rounded-xl shadow p-6 space-y-4">
      <h1 class="text-3xl font-bold text-green-800">Flóra & Ádám</h1>
      <p class="text-lg">Szokolya, Királyréti Vadászkastély – <strong>2025.09.13.</strong></p>
      <p class="italic">We've decided that from now on, we'll put up with each other forever. Come laugh, cry, and eat cake with us!</p>
      
      <div class="mt-4 space-y-2">
        <h2 class="font-semibold">Schedule</h2>
        <ul class="list-disc list-inside">
          <li>14:30 – Guest arrival</li>
          <li>15:30 – Ceremony</li>
          <li>16:00 – Photos</li>
          <li>19:00 – Dinner</li>
          <li>23:00 – Cake</li>
          <li>0:00 – Midnight snack</li>
          <li>0:45 – Moshpit till dawn</li>
        </ul>
        <h2 class="font-semibold mt-4">Address</h2>
        <p>Szokolya, Királyrét, 2624</p>
        <p>47.89533180751165, 18.97720805857283</p>
      </div>
    </div>

    <!-- RSVP Form -->
    <div class="bg-white border border-green-300 rounded-xl shadow p-6">
      <h2 class="text-2xl font-bold mb-4 text-green-800">Esküvői visszajelzés</h2>

      <form id="rsvp-form" class="space-y-6" onsubmit="handleSubmit(event)">
        <!-- Résztvevők itt -->
      </form>

      <div class="flex flex-wrap gap-4 mt-6">
        <button type="button" onclick="addParticipant()" class="bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg text-sm px-5 py-2.5">Vendég hozzáadása</button>
        <button type="submit" form="rsvp-form" class="bg-green-800 hover:bg-green-900 text-white font-medium rounded-lg text-sm px-5 py-2.5">Beküldés</button>
      </div>
    </div>
  </div>

  <!-- Résztvevő sablon -->
  <template id="participant-template">
    <fieldset class="bg-green-50 border border-green-300 rounded-lg p-4 participant relative space-y-4 shadow-sm">
      <legend class="text-lg font-semibold px-2 py-0 mb-0 text-green-900">Vendég
        <button type="button" onclick="removeParticipant(this)" class="absolute top-0 right-2 text-white bg-red-700 hover:bg-red-800 text-xs rounded-lg px-5 py-2.5">(mégse)</button>
      </legend>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Résztvevő teljes neve</label>
        <input type="text" name="name[]" placeholder="Teljes név" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5" required />
      </div>

      <div>
        <label class="text-sm font-medium text-gray-700 mb-0">Követsz-e valamilyen speciális étrendet?</label>
        <div class="flex flex-wrap gap-4 mt-1">
          <label class="inline-flex items-center gap-2 text-sm">
            <input type="checkbox" value="laktózmentes" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded" />
            <span>Laktózmentes</span>
          </label>
          <label class="inline-flex items-center gap-2 text-sm">
            <input type="checkbox" value="gluténmentes" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded" />
            <span>Gluténmentes</span>
          </label>
          <label class="inline-flex items-center gap-2 text-sm">
            <input type="checkbox" value="cukormentes" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded" />
            <span>Cukormentes</span>
          </label>
          <label class="inline-flex items-center gap-2 text-sm">
            <input type="checkbox" value="vegetáriánus" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded" />
            <span>Vegetáriánus</span>
          </label>
          <label class="inline-flex items-center gap-2 text-sm">
            <input type="checkbox" value="vegán" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded" />
            <span>Vegán</span>
          </label>
          <label class="inline-flex items-center gap-2 text-sm">
            <span>Egyéb:</span>
            <input type="text" name="diet_other[]" placeholder="Egyéb étrend" class="bg-white border border-gray-300 text-xs rounded-lg p-2 w-48" />
          </label>
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Van-e ételallergiád?</label>
        <input type="text" name="allergy[]" placeholder="Pl. dió, mogyoró..." class="bg-white border border-gray-300 text-sm rounded-lg p-2 w-full" />
      </div>
    </fieldset>
  </template>

  <script>
    function addParticipant() {
      const tmpl = document.getElementById("participant-template");
      const clone = tmpl.content.cloneNode(true);
      document.getElementById("rsvp-form").appendChild(clone);
    }

    function removeParticipant(btn) {
      btn.closest(".participant").remove();
    }

    function handleSubmit(event) {
      event.preventDefault();

      const form = document.getElementById("rsvp-form");
      const guests = [];

      const participants = form.querySelectorAll(".participant");
      participants.forEach(part => {
        const name = part.querySelector('input[name="name[]"]').value.trim();
        const allergy = part.querySelector('input[name="allergy[]"]').value.trim();
        const other = part.querySelector('input[name="diet_other[]"]').value.trim();

        const diets = [];
        part.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
          if (checkbox.checked) diets.push(checkbox.value);
        });
        if (other !== "") diets.push(other);

        guests.push({ név: name, diéták: diets, alergia: allergy });
      });

      fetch("index.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ guests })
      })
      .then(res => res.text())
      .then(html => {
        document.open(); document.write(html); document.close();
      });
    }

    window.onload = addParticipant;
  </script>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
</body>
</html>
