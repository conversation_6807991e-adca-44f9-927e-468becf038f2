<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<meta charset="utf-8">
	
	<!-- Page Title -->
	<title>Flóra & Ádám</title>

	<!-- Open Graph / Facebook Metadata -->
	<meta property="og:title" content="Flóra & Ádám Esküvő" />
	<meta property="og:description" content="Szeretettel meghívunk az esküvőnkre! Itt tudsz visszajelezni." />
	<meta property="og:image" content="https://www.fladam.hu/favico.png" />
	<meta property="og:url" content="https://www.fladam.hu/" />
	<meta property="og:type" content="website" />
	<meta property="og:locale" content="hu_HU" />
	
	<!-- Optional Twitter Card (if needed) -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content="Flóra & Ádám Meghívó" />
	<meta name="twitter:description" content="Szeretettel meghívunk az esküvőnkre!" />
	<meta name="twitter:image" content="https://www.fladam.hu/favico.png" />
	
	<meta name="keywords" content="Flóra, Ádám, wedding, esküvő">
	<meta name="description" content="Flóra & Ádám wedding esküvő">
	<meta name="author" content="<EMAIL>">
	
	<!-- Mobile Meta Tag -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	
	<!-- Fav and touch icons -->
	<link rel="icon" href="images/fav_touch_icons/favicon.ico" sizes="any">
	<link rel="icon" href="images/fav_touch_icons/favicon.svg" type="image/svg+xml">
	<link rel="apple-touch-icon" href="images/fav_touch_icons/apple-touch-icon-180x180.png">
	<link rel="manifest" href="images/fav_touch_icons/manifest.json">
	
	<!-- IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
	  <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script> 
	<![endif]-->
	
	<!-- Google Web Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300&display=swap" rel="stylesheet">
	
	<!-- Bootstrap CSS -->
	<link href="css/bootstrap.min.css" rel="stylesheet" />
	
	<!-- FontAwesome CSS -->
	<link href="css/fontawesome-all.min.css" rel="stylesheet" />
	
	<!-- Neela Icon Set CSS -->
	<link href="css/neela-icon-set.css" rel="stylesheet" />
	
	<!-- Owl Carousel CSS -->
	<link href="css/owl.carousel.min.css" rel="stylesheet" />
	
	<!-- Template CSS -->
	<link href="css/style.css" rel="stylesheet" />
	
	<!-- Modernizr JS -->
	<script src="js/modernizr-3.6.0.min.js"></script>

	<link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
	<script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>

	<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>


	<style>
		.gallery-scroller li img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
			margin: 0;
			padding: 0;
		}
		#menu-image {
		background-image: url("photos/image28.jpg");
		background-size: cover;
		background-position: center;
		background-attachment: scroll;

		}

		.nav-section .nav-logo {
			display: flex;
			align-items: center;  /* Vertically center */
		}

		.nav-logo h1 {
			font-size: 24px;
			line-height: 1.2;
			margin: 0;
			padding: 0;
			font-family: 'Poppins', sans-serif;
			font-weight: 300;
			color: #fff;
		}

		.btn-waze {
			background-color: #33CCFF;
			color: #fff;
			font-family: 'Poppins', sans-serif;
			font-weight: 300;
			font-size: 16px;
			font-weight: 500;
			padding: 10px 20px;
			border-radius: 5px;
			border: 1px solid #8eaeba;
			cursor: pointer;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			text-decoration: none;
		}

		.btn-waze:hover {
			border: 1px solid #fff;
		}

		.map-info-container::before, .timeline::before {
			display: none;
		}

		.btn-danger {
			color: #ac2925;
		}

		.btn-danger:hover {
			background-color: #c9302c;
			color: #fff;
		}

		@keyframes fadeInUp {
			0% { opacity: 0; transform: translateY(10px); }
			100% { opacity: 1; transform: translateY(0); }
			}

			.guest-fieldset {
			animation: fadeInUp 0.4s ease;
			}

			.form-check {
				display: inline-block;
				margin-right: 10px;
				min-width: 100px;
			}

			.btn-custom {
				font-family: 'Poppins', sans-serif;
				font-weight: 300;
				font-weight: 500;
				border-radius: 5px;
				background-color: #475D65; /* #8eaeba; */
				color: #fff;
				cursor: pointer;
				display: inline-flex;
				align-items: center;
				justify-content: center;
			}

			.btn-add {
				background-color: #666447;
				color: #fff;
			}

			@media (max-width: 768px) {
				.section-title-lg {
					font-size: 44px;
				}
			}

			.gallery-wrapper {
				margin-left: auto;
				margin-right: auto;
				max-width: 1905px;
			}
	</style>
    
</head>
<body>

	<!-- BEGIN PRELOADER -->
	<div id="preloader">
		<div class="loading-heart">
			<svg viewBox="0 0 512 512" width="100">
				<path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z" />
			</svg>
			<div class="preloader-title">
				Flóra<br>
				<small>&</small><br>
				Ádám
			</div>
		</div>
	</div>
	<!-- END PRELOADER -->


	<!-- BEGIN WRAPPER -->
	<?php
	// Elérhető nyelvek
	$supportedLanguages = ['en', 'hu'];

	// 1. Ha van ?lang= paraméter, és az érvényes → beállítjuk sütibe
	if (isset($_GET['lang']) && in_array($_GET['lang'], $supportedLanguages)) {
		$lang = $_GET['lang'];
		setcookie('lang', $lang, time() + (60 * 60 * 24 * 30)); // 30 napra elmentjük
	} 
	// 2. Ha van 'lang' süti → használjuk
	elseif (isset($_COOKIE['lang']) && in_array($_COOKIE['lang'], $supportedLanguages)) {
		$lang = $_COOKIE['lang'];
	} 
	// 3. Egyébként böngésző nyelv alapján próbálkozunk
	else {
		$browserLang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'en', 0, 2);
		$lang = in_array($browserLang, $supportedLanguages) ? $browserLang : 'en';
	}

	// 4. Megfelelő fájl betöltése
	include "home.$lang.php";
	?>

	<!-- END WRAPPER -->

	<script>
		window.siteLang = '<?= $lang ?>';
	</script>
	
	
	<!-- Google Maps API and Map Richmarker Library -->
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBHOXsTqoSDPQ5eC5TChvgOf3pAVGapYog"></script>
	<script src="js/richmarker.js"></script>
	
	<!-- Libs -->
	<script src="js/jquery-3.6.0.min.js"></script>
	<script src="js/jquery-ui.min.js"></script>
	<script src="js/jquery-migrate-3.3.2.min.js"></script>
	<script src="js/bootstrap.bundle.min.js"></script>
	<script src="js/jquery.placeholder.min.js"></script>
	<script src="js/ismobile.js"></script>
	<script src="js/retina.min.js"></script>
	<script src="js/waypoints.min.js"></script>
	<script src="js/waypoints-sticky.min.js"></script>
	<script src="js/owl.carousel.min.js"></script>
	<script src="js/lightbox.min.js"></script>
    
    <!-- Nicescroll script to handle gallery section touch slide -->
	<script src="js/jquery.nicescroll.js"></script>
    
    <!-- Hero Background Slideshow Script -->
	<script src="js/jquery.zoomslider.js"></script>
	
	<!-- Template Scripts -->
	<script src="js/variables.js"></script>

	<script>
		if (window.siteLang === 'hu') {
			window.c_days = 'NAP<br>&nbsp;';
			window.c_hours = 'ÓRA<br>&nbsp;';
			window.c_minutes = 'PERC<br>&nbsp;';
			window.c_seconds = 'MÁSOD<br>PERC';
			window.countdown_end_msg = 'Az esemény elkezdődött!';
		} 
	</script>

	<script src="js/scripts.js"></script>

	<script>
		$(document).ready(function () {
			$(".gallery-scroller").niceScroll({
				cursorcolor: "#fff",
				cursorwidth: "0px",
				background: "#fff",
				cursorborder: "0px solid #1F2326",
				zindex: "999",
				autohidemode: false,
				enablemousewheel: false,
				touchbehavior: true
			});

			function isInViewport(element) {
				const rect = element[0].getBoundingClientRect();
				return (
					rect.top >= 0 &&
					rect.left >= 0 &&
					rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
					rect.right <= (window.innerWidth || document.documentElement.clientWidth)
				);
			}

			let alreadyNudged = false;

			$(window).on('scroll', function () {
				const $gallery = $(".gallery-scroller");
				if (!alreadyNudged && isInViewport($gallery)) {
					alreadyNudged = true;

					const scrollAmount = 50; // adjust for how far right you want to scroll
					const duration = 200; // milliseconds per scroll

					function nudgeScroll(times) {
						if (times === 0) return;
						$gallery.animate({ scrollLeft: "+=" + scrollAmount }, duration, function () {
							$gallery.animate({ scrollLeft: "-=" + scrollAmount }, duration, function () {
								setTimeout(() => nudgeScroll(times - 1), 100);
							});
						});
					}

					nudgeScroll(2); // do the nudge 2 times
				}
			});
		});
	</script>

	<script>
		mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';

		const map = new mapboxgl.Map({
			container: 'map_canvas',
			style: 'mapbox://styles/mapbox/streets-v11',
			center: [18.97720805857283, 47.89533180751165],
			zoom: 15
		});

		new mapboxgl.Marker()
			.setLngLat([18.97720805857283, 47.89533180751165])
			.setPopup(new mapboxgl.Popup().setHTML('<strong>Királyréti Vadászkastély</strong>'))
			.addTo(map);
	</script>

<script>
	document.addEventListener('alpine:init', () => {
		Alpine.data('rsvpForm', () => ({
			guests: [{ name: '', diet: [], diet_other: '', allergy: '' }],
			high_chair: false,
			contact: '',
			accommodation: '',
			special_notes: '',
			isSubmitting: false,
			success: false,
			errorMessage: '',
			dietLabels: {
				lactose: 'Lactose-free',
				gluten: 'Gluten-free',
				sugar: 'Sugar-free',
				vegetarian: 'Vegetarian',
				vegan: 'Vegan'
			},
			submitForm() {
				this.isSubmitting = true;
				const guestData = this.guests.map(g => ({
					name: g.name,
					diet_lactose: g.diet.includes('lactose'),
					diet_gluten: g.diet.includes('gluten'),
					diet_sugar: g.diet.includes('sugar'),
					diet_vegetarian: g.diet.includes('vegetarian'),
					diet_vegan: g.diet.includes('vegan'),
					special_diet: g.diet_other,
					allergy: g.allergy
				}));

				const payload = {
					guests: guestData,
					contact: this.contact,
					accomodation: this.accommodation,
					special_request: this.special_notes,
					child_chair: this.high_chair,
					lang: window.siteLang || 'en'
				};

				this.errorMessage = '';

				fetch('submit.php', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(payload)
				})
					.then(res => res.json())
					.then(data => {
						this.isSubmitting = false;
						if (data.success) {
							this.success = true;
						} else {
							this.errorMessage = data.error || 'Something went wrong during submission.';
						}
					})
					.catch(() => {
						this.isSubmitting = false;
						this.errorMessage = 'Something went wrong during submission.';
					});
			}
		}));
	});
</script>

<script>
  /*$(document).ready(function () {
    $('.hover-info').on('click', function (e) {
      e.stopPropagation(); // optional: prevent bubbling
      $(this).find('a')[0].click();
    });
  });*/
</script>
  
	
</body>
</html>