@charset "UTF-8";

@font-face {
  font-family: "Neela-icon-set";
  src:url("../webfonts/neela-icon-set.eot");
  src:url("../webfonts/neela-icon-set.eot?#iefix") format("embedded-opentype"),
    url("../webfonts/neela-icon-set.woff") format("woff"),
    url("../webfonts/neela-icon-set.ttf") format("truetype"),
    url("../webfonts/neela-icon-set.svg#neela-icon-set") format("svg");
  font-weight: normal;
  font-style: normal;

}

[data-icon]:before {
  font-family: "Neela-icon-set" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "Neela-icon-set" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-big-church:before {
  content: "\61";
}
.icon-champagne-glasses:before {
  content: "\62";
}
.icon-diamond-ring:before {
  content: "\63";
}
.icon-honeymoon:before {
  content: "\64";
}
.icon-two-hearts:before {
  content: "\65";
}
.icon-wedding:before {
  content: "\66";
}
.icon-wedding-day:before {
  content: "\67";
}
.icon-photo-camera:before {
  content: "\68";
}
