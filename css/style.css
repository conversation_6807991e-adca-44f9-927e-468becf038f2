@charset "UTF-8";
/*
* Author: Wisely Themes
* Author URI: http://www.wiselythemes.com
* Template Name: Neela
* Version: 1.0.1
*/
/* Table of Content
==================================================
	#Playfair Display Font
	#Variables
	#Mixins
	#Sections with background image
	#Typography
	#General
	#Header
	#Navigation
	#Hero
	#About Us
	#Our Story
	#Invite
	#Wedding Location
	#Bridesmaids & Groomsmen
	#Testimonials
	#Gift Registry
	#Gallery
	#Blog
	#Widgets
	#RSVP
	#Footer
	#Lightbox
	#Media Queries */
/* #Playfair Display Font
================================================== */
@font-face {
  font-family: "Playfair Display";
  src: url("../webfonts/playfairdisplay-regular.woff2") format("woff2"), url("../webfonts/playfairdisplay-regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}
/* #Variables
================================================== */
/* #Mixins
================================================== */
#footer .copyright a, .widget-quick-links > ul li a, .contact-info li a, .widget-tags .tags a, .widget-latest-posts > ul h3 a, .widget-categories > ul li a, .comments .comment-list > li .comment .reply-btn,
.comments .comment-list > li .comment-info .reply-btn, .blog-listing .item .blog-meta li a,
.blog-listing .post-content .blog-meta li a,
.blog-main .item .blog-meta li a,
.blog-main .post-content .blog-meta li a {
  background: linear-gradient(#8eaeba, #8eaeba) no-repeat 0 100%;
  background-size: 0 1px;
  -moz-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  -o-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  -webkit-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  transition: background-size 0.3s ease-out, color 0.3s ease-out;
}
#footer .copyright a:hover, .widget-quick-links > ul li a:hover, .contact-info li a:hover, .widget-tags .tags a:hover, .widget-latest-posts > ul h3 a:hover, .widget-categories > ul li a:hover, .comments .comment-list > li .comment .reply-btn:hover,
.comments .comment-list > li .comment-info .reply-btn:hover, .blog-listing .item .blog-meta li a:hover,
.blog-listing .post-content .blog-meta li a:hover,
.blog-main .item .blog-meta li a:hover,
.blog-main .post-content .blog-meta li a:hover {
  color: #8eaeba;
  background-size: 100% 1px;
}

/* #Sections with background image
================================================== */
#our-story-title {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #our-story-title {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#the-wedding {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #the-wedding {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#bridesmaids,
#bmiadsgmen {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #bridesmaids,
#bmiadsgmen {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#groomsmen {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #groomsmen {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#giftregistry {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #giftregistry {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

.page-header {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  .page-header {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#menu-image {
  background-image: url("https://via.placeholder.com/970x890.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #menu-image {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

#our-partners {
  background-image: url("https://via.placeholder.com/1920x1080.png");
}
@media (min--moz-device-pixel-ratio: 1.3), (-o-min-device-pixel-ratio: 2.6/2), (-webkit-min-device-pixel-ratio: 1.3), (min-device-pixel-ratio: 1.3), (min-resolution: 1.3dppx) {
  #our-partners {
    background-image: url("https://via.placeholder.com/<EMAIL>");
  }
}

/* #Typography
================================================== */
a:focus {
  outline: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  color: #8eaeba;
  text-rendering: optimizelegibility;
}

h1 {
  font-size: 42pt;
}

h2 {
  font-size: 36pt;
}

h3 {
  font-size: 30pt;
}

h4 {
  font-size: 21pt;
}

h5 {
  font-size: 16pt;
}

/* #General
================================================== */
html {
  overflow: auto;
}

body {
  padding: 0;
  height: 100%;
  color: #73777b;
  overflow: hidden;
  line-height: 1.6;
  background-color: #ffffff;
  font-family: "Poppins", sans-serif;
  font-size: 15px;
}

::selection {
  background: #8eaeba;
  color: #fff;
}

::-moz-selection {
  background: #8eaeba;
  color: #fff;
}

.color {
  color: #8eaeba;
}

a {
  color: #8eaeba;
  text-decoration: none;
}

a:hover,
a:focus {
  color: #8eaeba;
}

iframe[src="about:blank"] {
  display: none;
}

p {
  margin: 0 0 20px;
}

img {
  max-width: 100%;
  border: 0 none;
  vertical-align: middle;
}

input,
button,
select,
textarea {
  font-family: "Poppins", sans-serif;
}

#preloader {
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1002;
  text-align: center;
}
#preloader .preloader-title {
  font-size: 20pt;
  color: currentColor;
  line-height: 0.9;
  position: absolute;
  top: 41px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  font-family: "Playfair Display", serif;
}
#preloader .preloader-title small {
  color: currentColor;
  line-height: 0.9;
  font-size: 12pt;
}

.loading-heart {
  fill: transparent;
  stroke: #8eaeba;
  stroke-width: 11;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  display: inline-block;
}
.loading-heart svg {
  overflow: visible;
  width: 11rem;
}
.loading-heart path {
  stroke-dashoffset: 0;
  stroke-dasharray: 1550;
  transform-origin: center;
  -webkit-animation: stroke-animation 3s ease-in-out infinite forwards;
  -khtml-animation: stroke-animation 3s ease-in-out infinite forwards;
  -moz-animation: stroke-animation 3s ease-in-out infinite forwards;
  -ms-animation: stroke-animation 3s ease-in-out infinite forwards;
  -o-animation: stroke-animation 3s ease-in-out infinite forwards;
  animation: stroke-animation 3s ease-in-out infinite forwards;
}

@keyframes stroke-animation {
  0% {
    stroke-dashoffset: 0;
  }
  30% {
    stroke-dashoffset: 1550;
  }
  60% {
    stroke-dashoffset: 3100;
    fill: transparent;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    stroke-dashoffset: 3100;
  }
}
#wrapper {
  position: relative;
  overflow: hidden;
  top: 0;
  left: 0;
  z-index: 99;
  height: 100%;
  min-height: 100vh;
  background-color: #fff;
  -webkit-transition: -webkit-transform 0.5s;
  transition: transform 0.5s;
  -webkit-backface-visibility: hidden;
}
#wrapper::after {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  background: transparent;
  content: "";
  z-index: 1002;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  -moz-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  -ms-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  -o-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
}
#wrapper.open {
  -webkit-transform: translate3d(-300px, 0, 0);
  transform: translate3d(-300px, 0, 0);
}
#wrapper.open::after {
  width: 100%;
  height: 100%;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -moz-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  -webkit-transition: opacity 0.5s;
  transition: opacity 0.5s;
}

section {
  position: relative;
  padding: 70px 0;
  background-color: #ffffff;
  z-index: 0;
}

.section-bg-color {
  background-color: #f9f9f9;
}

.section-title {
  float: none;
  text-align: center;
  margin: 0 auto 70px;
  position: relative;
  padding-bottom: 35px;
  font-family: "Playfair Display", serif;
}
.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 189px;
  max-width: 189px;
  height: 24px;
  background-repeat: no-repeat;
  background-position-x: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='189' height='26' viewBox='0 0 189 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M86.19%2C2.362L96.98%2C13%2C86.19%2C23.634%2C75.4%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M94.985%2C2.362L105.775%2C13%2C94.985%2C23.634%2C84.2%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M103.78%2C2.362L114.57%2C13%2C103.78%2C23.634%2C92.991%2C13Z'/%3E%3Cpath fill-rule='evenodd' fill='%238eaeba' d='M120%2C14V13H523v1H120Z'/%3E%3Cpath fill-rule='evenodd' fill='%238eaeba' d='M-331%2C14V13H69v1H-331Z'/%3E%3C/svg%3E");
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
.section-title.light {
  color: #fff;
}
.section-title.light::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='189' height='26' viewBox='0 0 189 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M86.19%2C2.362L96.98%2C13%2C86.19%2C23.634%2C75.4%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M94.985%2C2.362L105.775%2C13%2C94.985%2C23.634%2C84.2%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M103.78%2C2.362L114.57%2C13%2C103.78%2C23.634%2C92.991%2C13Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M120%2C14V13H523v1H120Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M-331%2C14V13H69v1H-331Z'/%3E%3C/svg%3E");
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.section-title.desc {
  margin-bottom: 35px;
}

.section-title-lg {
  float: none;
  text-align: center;
  margin: 0 auto 70px;
  position: relative;
  font-size: 62pt;
  font-family: "Playfair Display", serif;
  line-height: 0.7;
}
.section-title-lg.light {
  color: #fff;
}
.section-title-lg small {
  font-size: 38pt;
  margin-top: 40px;
  position: relative;
  top: -20px;
  -khtml-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  font-weight: 400;
  display: block;
}
.section-title-lg.desc {
  margin-bottom: 50px;
}

.section-desc {
  clear: both;
  margin-bottom: 40px;
}
.section-desc.light {
  color: #fff;
}

.cta {
  color: #8eaeba;
  font-size: 16pt;
}

.bg-color {
  background-color: #8eaeba;
  color: #fff;
}
.bg-color a:not(.btn) {
  color: #fff;
  background: linear-gradient(#fff, #fff) no-repeat 0 100%;
  background-size: 100% 1px;
  -moz-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  -o-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  -webkit-transition: background-size 0.3s ease-out, color 0.3s ease-out;
  transition: background-size 0.3s ease-out, color 0.3s ease-out;
}
.bg-color a:not(.btn):hover {
  color: #fff;
  background-size: 0 1px;
}
.bg-color i,
.bg-color h1,
.bg-color h2,
.bg-color h3,
.bg-color h4,
.bg-color h5,
.bg-color .sn-icons a {
  color: #fff;
}

.bg-color-overlay::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #3d5963;
  z-index: -1;
  -khtml-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}

.center {
  float: none;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  align-items: center;
  justify-content: center;
}

p.center {
  margin-bottom: 20px;
}

.h-lines,
.v-lines {
  position: absolute;
  top: var(--horizontal-offset, 0);
  right: var(--vertical-offset, 0);
  bottom: var(--horizontal-offset, 0);
  left: var(--vertical-offset, 0);
  transition: transform 0.8s ease;
  will-change: transform;
  z-index: -1;
}

.h-lines::before,
.v-lines::before {
  content: "";
  position: absolute;
  border: inherit;
}

.h-lines {
  --vertical-offset: calc(var(--offset) * -1);
  border-top: var(--border-size) solid currentcolor;
  border-bottom: var(--border-size) solid currentcolor;
  top: calc(var(--border-size) * -1);
  bottom: calc(var(--border-size) * -1);
}

.h-lines::before {
  top: calc(var(--vertical-offset) - var(--border-size));
  bottom: calc(var(--vertical-offset) - var(--border-size));
  left: calc(var(--offset) - var(--border-size));
  right: calc(var(--offset) - var(--border-size));
}

.v-lines {
  --horizontal-offset: calc(var(--offset) * -1);
  border-left: var(--border-size) solid currentcolor;
  border-right: var(--border-size) solid currentcolor;
  right: calc(var(--border-size) * -1);
  left: calc(var(--border-size) * -1);
}

.v-lines::before {
  top: calc(var(--offset) - var(--border-size));
  bottom: calc(var(--offset) - var(--border-size));
  left: calc(var(--horizontal-offset) - var(--border-size));
  right: calc(var(--horizontal-offset) - var(--border-size));
}

.btn {
  --offset: 7px;
  --border-size: 1px;
  display: inline-block;
  color: #8eaeba;
  background: transparent;
  position: relative;
  margin: 15px;
  padding: 15px 35px;
  border: 1px solid currentcolor;
  font-weight: 400;
  font-size: 1rem;
  font-family: "Playfair Display", serif;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  line-height: 1;
  outline: none;
  cursor: pointer;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.btn:hover, .btn:focus, .btn:active {
  background: transparent;
  border-color: #8eaeba;
  color: #8eaeba;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn:hover .h-lines, .btn:focus .h-lines, .btn:active .h-lines {
  transform: scaleX(0);
}
.btn:hover .v-lines, .btn:focus .v-lines, .btn:active .v-lines {
  transform: scaleY(0);
}
.btn > i {
  margin-right: 10px;
}
.btn > i.after {
  margin-right: 0;
  margin-left: 10px;
}
.btn.reverse .h-lines {
  transform: scaleX(0);
}
.btn.reverse .v-lines {
  transform: scaleY(0);
}
.btn.reverse:hover .h-lines, .btn.reverse:focus .h-lines, .btn.reverse:active .h-lines {
  transform: scaleX(1);
}
.btn.reverse:hover .v-lines, .btn.reverse:focus .v-lines, .btn.reverse:active .v-lines {
  transform: scaleY(1);
}
.btn.only-icon > i {
  margin-right: 0;
}

.btn-primary {
  color: #8eaeba;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background: rgba(142, 174, 186, 0.05);
  border-color: #8eaeba;
  color: #8eaeba;
}
.btn-primary:active:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(142, 174, 186, 0.2);
  -moz-box-shadow: 0 0 0 0.25rem rgba(142, 174, 186, 0.2);
  box-shadow: 0 0 0 0.25rem rgba(142, 174, 186, 0.2);
}
.btn-primary.disabled, .btn-primary:disabled {
  color: #8eaeba;
  background-color: rgba(142, 174, 186, 0.05);
  border-color: #8eaeba;
}
.btn-primary.disabled .h-lines, .btn-primary:disabled .h-lines {
  transform: scaleX(0);
}
.btn-primary.disabled .v-lines, .btn-primary:disabled .v-lines {
  transform: scaleY(0);
}

.btn-light {
  color: #fff;
}
.btn-light:hover, .btn-light:focus, .btn-light:active {
  background: rgba(255, 255, 255, 0.2);
  border-color: #fff;
  color: #fff;
}
.btn-light:active:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.2);
  -moz-box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.2);
}
.btn-light.disabled, .btn-light:disabled {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-color: #fff;
}
.btn-light.disabled .h-lines, .btn-light:disabled .h-lines {
  transform: scaleX(0);
}
.btn-light.disabled .v-lines, .btn-light:disabled .v-lines {
  transform: scaleY(0);
}

.btn-dark {
  color: #fff;
  background: rgba(115, 119, 123, 0.8);
  border: 2px solid #73777b;
}
.btn-dark .h-lines {
  border-top-color: #73777b;
  border-bottom-color: #73777b;
}
.btn-dark .v-lines {
  border-left-color: #73777b;
  border-right-color: #73777b;
}
.btn-dark:hover, .btn-dark:focus, .btn-dark:active {
  background: rgba(115, 119, 123, 0.1);
  border-color: #73777b;
  color: #73777b;
}
.btn-dark:active:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(115, 119, 123, 0.2);
  -moz-box-shadow: 0 0 0 0.25rem rgba(115, 119, 123, 0.2);
  box-shadow: 0 0 0 0.25rem rgba(115, 119, 123, 0.2);
}
.btn-dark.disabled, .btn-dark:disabled {
  background: rgba(115, 119, 123, 0.1);
  border-color: #73777b;
  color: #73777b;
}
.btn-dark.disabled .h-lines, .btn-dark:disabled .h-lines {
  transform: scaleX(0);
}
.btn-dark.disabled .v-lines, .btn-dark:disabled .v-lines {
  transform: scaleY(0);
}

.btn-sm {
  padding: 0 20px;
  font-size: 0.9rem;
  font-size: 0.9rem;
  height: 30px;
  line-height: 27px;
}

[data-animation-delay] {
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.visible[data-animation-delay] {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}

.animate-fade {
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.animate-from-top {
  -webkit-transform: translateY(-40px);
  -moz-transform: translateY(-40px);
  -ms-transform: translateY(-40px);
  -o-transform: translateY(-40px);
  transform: translateY(-40px);
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.animate-from-bottom {
  -webkit-transform: translateY(40px);
  -moz-transform: translateY(40px);
  -ms-transform: translateY(40px);
  -o-transform: translateY(40px);
  transform: translateY(40px);
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.animate-from-left {
  -webkit-transform: translateX(-40px);
  -moz-transform: translateX(-40px);
  -ms-transform: translateX(-40px);
  -o-transform: translateX(-40px);
  transform: translateX(-40px);
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.animate-from-right {
  -webkit-transform: translateX(40px);
  -moz-transform: translateX(40px);
  -ms-transform: translateX(40px);
  -o-transform: translateX(40px);
  transform: translateX(40px);
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}

.animation-fade {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition-duration: 1.5s;
  -moz-transition-duration: 1.5s;
  -o-transition-duration: 1.5s;
  transition-duration: 1.5s;
}

.animation-from-bottom,
.animation-from-top {
  -webkit-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -moz-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -o-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition-duration: 1.5s;
  -moz-transition-duration: 1.5s;
  -o-transition-duration: 1.5s;
  transition-duration: 1.5s;
}

.animation-from-left,
.animation-from-right {
  -webkit-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -moz-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -o-transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  transition-timing-function: cubic-bezier(0.2, 0.75, 0.25, 0.9);
  -webkit-transform: translateX(0px);
  -moz-transform: translateX(0px);
  -ms-transform: translateX(0px);
  -o-transform: translateX(0px);
  transform: translateX(0px);
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition-duration: 1.5s;
  -moz-transition-duration: 1.5s;
  -o-transition-duration: 1.5s;
  transition-duration: 1.5s;
}

.parallax {
  position: relative;
  overflow: hidden;
  background-position: center center;
}

.parallax-background {
  width: 100%;
  background-attachment: fixed;
  background-repeat: repeat-y;
  -webkit-background-size: cover;
  background-size: cover;
}

.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  margin-bottom: 70px;
}
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  touch-action: manipulation;
  -moz-backface-visibility: hidden;
}
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}
.owl-carousel .owl-item {
  position: relative;
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 0.4s ease;
}
.owl-carousel .owl-item .owl-lazy:not([src]),
.owl-carousel .owl-item .owl-lazy[src^=""] {
  max-height: 0;
}
.owl-carousel .owl-item .item {
  margin: 0 2px;
  position: relative;
  border: 10px solid #fff;
  overflow: hidden;
}
.owl-carousel .owl-item .item img {
  width: 100%;
  height: auto;
}
.owl-carousel .owl-item .item:hover img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
.owl-carousel .owl-item .item .hover-img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: block;
  background-color: rgba(86, 125, 140, 0.7);
  border: 10px solid transparent;
  z-index: 9;
  text-align: center;
  visibility: hidden;
  text-decoration: none;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.owl-carousel .owl-item .item .hover-img .btn {
  position: absolute;
  z-index: 2;
  top: -32px;
  left: 50%;
  margin: 0;
  font-size: 16pt;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.owl-carousel .owl-item .item:hover > .hover-img {
  visibility: visible;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.owl-carousel .owl-item .item:hover > .hover-img .btn {
  top: 50%;
}
.owl-carousel .owl-item .item img,
.owl-carousel .owl-item .hover-img {
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}
.owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}
.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
  display: none;
}
.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.owl-carousel .owl-nav {
  border: 10px solid #fff;
  display: inline-block;
  position: absolute;
  left: -15px;
  bottom: -30px;
  background-color: #fff;
  z-index: 99;
}
.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev {
  background: 0 0;
  font: inherit;
  width: 38px;
  border: 1px solid #8eaeba;
  background-color: #fff;
  color: #8eaeba;
  font-size: 22pt;
  padding-bottom: 6px;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.owl-carousel .owl-nav button.owl-next:hover,
.owl-carousel .owl-nav button.owl-prev:hover {
  background-color: #8eaeba;
  color: #fff;
}
.owl-carousel .owl-nav button.owl-prev {
  margin-right: 10px;
}
.owl-carousel .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent;
  position: absolute;
  bottom: -45px;
  left: 0;
  width: 100%;
}
.owl-carousel .owl-dots .owl-dot {
  color: inherit;
  display: inline-block;
  border: 0;
  background: transparent;
  padding: 0;
  transform: scale(1);
  *display: inline;
}
.owl-carousel .owl-dots .owl-dot:focus {
  outline: none;
}
.owl-carousel .owl-dots .owl-dot span, .owl-carousel .owl-dots .owl-dot:focus span {
  width: 20px;
  height: 3px;
  margin: 5px;
  background: #73777b;
  display: block;
  -webkit-backface-visibility: visible;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.owl-carousel .owl-dots .owl-dot.active span, .owl-carousel .owl-dots .owl-dot:hover span {
  height: 6px;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.owl-carousel.light .owl-dots .owl-dot span, .owl-carousel.light .owl-dots .owl-dot:focus span {
  background: #fff;
}
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}
.owl-carousel.owl-hidden {
  opacity: 0;
}
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}
.owl-carousel.owl-drag .owl-item {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}
.owl-carousel.owl-rtl {
  direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
  float: right;
}
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
  z-index: 0;
}
.owl-carousel .owl-animated-out {
  z-index: 1;
}
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform 0.1s ease;
}
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 0.4s ease;
}
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

.owl-height {
  transition: height 0.5s ease-in-out;
}

.no-js .owl-carousel,
.owl-carousel.owl-loaded {
  display: block;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.uppercase {
  text-transform: uppercase;
}

.no-padding {
  padding: 0 !important;
}

.no-padding-top {
  padding-top: 0;
}

.no-padding-bottom {
  padding-bottom: 0;
}

.extra-padding-section {
  padding: 250px 0;
}

.extra-padding-top {
  padding-top: 70px;
}

.extra-margin-section {
  margin: 50px 0;
}

.extra-margin {
  margin: 50px 0 !important;
}

.mt-40 {
  margin-top: 40px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-40 {
  margin-bottom: 40px;
}

.pattern {
  background-image: url("../images/neela-pattern.png");
  background-repeat: repeat;
}

.padding-divider-top {
  padding-top: 170px;
}

.padding-divider-bottom {
  padding-top: 170px;
}

.section-divider-top-1 {
  padding-top: 170px !important;
}
.section-divider-top-1.off-section {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 130px;
}
.section-divider-top-1::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 130px 100vw 0 0;
  border-color: #fff transparent transparent transparent;
}
.section-divider-top-1.section-divider-bg-color::before {
  border-color: #f9f9f9 transparent transparent transparent;
}

.section-divider-top-2 {
  padding-top: 170px !important;
}
.section-divider-top-2.off-section {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 130px;
}
.section-divider-top-2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 130px 100vw;
  border-color: transparent transparent #fff transparent;
}
.section-divider-top-2.section-divider-bg-color::before {
  border-color: transparent transparent #f9f9f9 transparent;
}

.section-divider-bottom-1 {
  padding-bottom: 145px !important;
}
.section-divider-bottom-1.off-section {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 130px;
}
.section-divider-bottom-1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 130px 100vw;
  border-color: transparent transparent #fff transparent;
}
.section-divider-bottom-1.section-divider-bg-color::after {
  border-color: transparent transparent #f9f9f9 transparent;
}

.section-divider-bottom-2 {
  padding-bottom: 170px !important;
}
.section-divider-bottom-2.off-section {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 130px;
}
.section-divider-bottom-2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 150px 0 0 100vw;
  border-color: transparent transparent transparent #fff;
}
.section-divider-bottom-2.section-divider-bg-color::after {
  border-color: transparent transparent transparent #f9f9f9;
}

.screen-reader-text {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  word-wrap: normal !important;
}

/* #Header
================================================== */
#header {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.nav-section {
  padding: 0;
  min-height: 70px;
  background: none repeat scroll 0 0 #ffffff;
  text-align: center;
}
.nav-section .nav-logo {
  float: left;
  padding: 5px 0;
  height: 65px;
  position: relative;
}
.nav-section .nav-logo img {
  max-height: 100%;
  width: auto;
  object-fit: contain;
}
.nav-section.light {
  background: transparent;
  border-bottom: 0;
  -moz-transition: background 0.3s;
  -o-transition: background 0.3s;
  -webkit-transition: background 0.3s;
  transition: background 0.3s;
}
.nav-section.light.sticky {
  background: #8eaeba;
}

/* #Navigation
================================================== */
.navbar {
  float: right;
  min-height: 0;
  border: none;
  padding-top: 0;
  padding-bottom: 0;
  font-family: "Playfair Display", serif;
}

.nav .open > a, .nav .open > a:hover, .nav .open > a:focus,
.nav > li > a:hover, .nav > li > a:focus,
.navbar-nav > li > a.active {
  background-color: transparent;
  color: #8eaeba;
}

.navbar-nav {
  display: block;
}
.navbar-nav > li {
  display: inline-block;
}
.navbar-nav > li.dropdown > a .caret {
  border-right: 3px solid rgba(0, 0, 0, 0);
  border-top: 3px solid;
  margin-left: 10px;
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-left: 4px solid transparent;
}
.navbar-nav > li > a {
  padding: 0 20px;
  display: block;
  color: #73777b;
  line-height: 70px;
  text-transform: uppercase;
  font-size: 12pt;
  text-align: center;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.navbar-nav .dropdown-menu {
  position: absolute;
  top: 70px;
  background-color: rgba(255, 255, 255, 0.94);
  text-align: center;
  padding: 0;
  border: none;
  display: none;
  font-size: 13pt;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.176);
  left: 50%;
  margin-left: -80px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
.navbar-nav .open > .dropdown-menu,
.navbar-nav .dropdown-menu:hover {
  display: block;
}
.navbar-nav .dropdown-menu > li > a {
  display: block;
  color: #73777b;
  line-height: 1;
  padding: 12px;
  position: relative;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}
.navbar-nav .dropdown-menu > li > a::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 86%;
  left: 7%;
  bottom: 0;
  background-color: #e2e7ec;
}
.navbar-nav .dropdown-menu > li:last-child a::before {
  height: 0;
}
.navbar-nav .dropdown-menu > li > a:hover,
.navbar-nav .dropdown-menu > li > a:focus,
.navbar-nav .dropdown-menu > li > a.active {
  background-color: #fff;
  color: #8eaeba;
}
.navbar-nav .dropdown-submenu {
  position: relative;
}
.navbar-nav .dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: 0px;
  margin-left: -1px;
  max-height: 0;
  overflow: hidden;
}
.navbar-nav .dropdown-submenu > .dropdown-menu > li > a::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 86%;
  left: 7%;
  bottom: 0;
  background-color: #e2e7ec;
}
.navbar-nav .dropdown-submenu > .dropdown-menu > li:last-child a::before {
  height: 0;
}
.navbar-nav .dropdown-submenu:hover > .dropdown-menu {
  max-height: 500px;
  overflow: visible;
}
.navbar-nav .dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 4px 0 4px 4px;
  border-left-color: #73777b;
  position: absolute;
  top: 50%;
  margin-top: -3px;
  right: 8px;
}
.navbar-nav .dropdown-submenu:hover > a:after {
  border-left-color: #8eaeba;
}
.navbar-nav .dropdown-submenu:hover .dropdown-menu {
  display: block;
}
.navbar-nav .dropdown-submenu .pull-left {
  float: none;
}
.navbar-nav .dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
}

.navbar-nav > li.dropdown:hover > .dropdown-menu {
  display: block;
}

.light .nav .open > a, .light .nav .open > a:hover, .light .nav .open > a:focus,
.light .nav > li > a:hover, .light .nav > li > a:focus,
.light .navbar-nav > li > a.active {
  color: #fff;
}
.light .navbar-nav > li > a {
  color: rgba(255, 255, 255, 0.7);
}

#nav-mobile-btn {
  background-color: #8eaeba;
  border: 0;
  color: #fff;
  font-size: 28px;
  width: 40px;
  padding: 5px 0;
  margin-top: 14px;
  line-height: 1;
  text-align: center;
  float: right;
  display: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
#nav-mobile-btn:active, #nav-mobile-btn:hover {
  background-color: #5f8c9c;
}

.light #nav-mobile-btn {
  background-color: transparent;
  color: #fff;
}
.light #nav-mobile-btn:active, .light #nav-mobile-btn:hover {
  background-color: #8eaeba;
}
.light.sticky #nav-mobile-btn:active, .light.sticky #nav-mobile-btn:hover {
  background-color: #fff;
  color: #8eaeba;
}

.nav-mobile {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 100;
  visibility: hidden;
  width: 300px;
  height: 100%;
  background: #F7F8FA;
  padding: 15px;
  overflow: auto;
  -webkit-transform: translate3d(50%, 0, 0);
  transform: translate3d(50%, 0, 0);
  border-left: 5px solid rgba(142, 174, 186, 0.3);
  -moz-transition: all 0.5s;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}
.nav-mobile > i {
  position: absolute;
  top: 6px;
  right: 15px;
  font-size: 28px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: transparent;
  -moz-transition: all 0.5s;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.nav-mobile > i:hover, .nav-mobile > i:active {
  color: #4e5153;
  background-color: #f1f3f6;
}
.nav-mobile h2 {
  text-align: center;
  margin: 10px 0 25px;
  font-size: 26pt;
}
.nav-mobile h2 i {
  margin-right: 10px;
}
.nav-mobile a {
  color: #73777b;
}
.nav-mobile a:hover {
  color: #8eaeba;
  text-decoration: none;
}
.nav-mobile a:focus {
  text-decoration: none;
}
.nav-mobile ul {
  list-style: none;
}
.nav-mobile > ul {
  padding: 0;
}
.nav-mobile > ul li {
  margin-bottom: 10px;
}
.nav-mobile > ul li > a {
  margin-bottom: 10px;
  display: block;
}
.nav-mobile > ul > li {
  margin-bottom: 20px;
  padding-left: 10px;
}
.nav-mobile > ul ul {
  padding-left: 15px;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
}

.nav-mobile::after {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  content: "";
  display: none;
  opacity: 1;
  -moz-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  -webkit-transition: opacity 0.5s;
  transition: opacity 0.5s;
}

.nav-mobile.open {
  visibility: visible;
  -webkit-transition: -webkit-transform 0.5s;
  transition: transform 0.5s;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.nav-mobile.open::after {
  width: 0;
  height: 0;
  opacity: 0;
  -webkit-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
}

.stuck {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}

.stuck #mobile-nav {
  margin-right: 10%;
}

/* #Hero
================================================== */
#hero {
  position: relative;
  min-height: 200px;
  background-color: transparent;
  text-align: center;
  overflow: hidden;
  padding: 75px 0;
  z-index: 0;
}
#hero::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #3d5963;
  z-index: 0;
  -khtml-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}
#hero > .container {
  position: relative;
}

.hero-wrapper {
  position: relative;
  padding: 90px 0;
}
.hero-wrapper::before, .hero-wrapper::after {
  content: "";
  width: 278px;
  height: 503px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: -1;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='278' height='503' viewBox='0 0 278 503'%3E%3Cpath fill='%238eaeba' fill-rule='evenodd' d='M241%2C443.8c-8.1-0.4-15.8%2C6.3-22.4%2C6.2c-8.9-0.2-14.9-4.8-21.3-1.1c7.8-4.9%2C19.7-10.8%2C27.5-19.2%09c-14-3.4-23.6%2C10.6-34.4%2C17.1c7.2-8.9%2C15.3-21.4%2C17.9-35.8c-8.3%2C10.7-18%2C18.9-22.8%2C36.6c-14.7%2C1.5-36-6.2-46-16.2%20c21.3%2C10.2%2C36.9%2C4.6%2C52.4-10.8c4.9-4.9%2C5.3-3.3%2C7.8-4.1c-7.9-8.8-31.5-7.7-42.8-1.9c-0.5%2C0.3-1%2C0.6-1.5%2C0.9c-2.9-2.5-4.2%2C0-5.7-0.8%20c-4.9-2.6-10.7-8.9-12.4-13.1c4.5%2C2.2%2C9.4%2C3.6%2C14.4%2C4.1c2.9%2C0.4%2C4.1%2C3.4%2C6.1%2C1.7c1.7-1.5%2C1.1-4.3-0.7-4.5c-1.8-0.2-4.1%2C1.7-5.6%2C1.6%20c-3.6-0.1-8.6-0.9-11.8-3.6c-1-0.8-1.8-1.7-2.4-2.8c3.3-3.5%2C7.3-6.3%2C11.8-7.9c1.6%2C1.2%2C3%2C3.4%2C5.1%2C1.2c1.1-1.2%2C0.6-4.5-1.4-4.7%20c-2.1-0.2-2.9%2C1.9-3.9%2C2.2c-5%2C1.2-8.2%2C5.5-12.5%2C7.5c-1.4-0.6-2.8-1.9-5.1-1.4c-4.7-2.1-9.3-4.5-13.8-7.2c13.4-1.1%2C26.6-4.2%2C39.2-9.1%20c1.6%2C2.1%2C3.8%2C0.5%2C5.9%2C0.9c2.7%2C8.4%2C17.1%2C17.4%2C24.9%2C13.6c2.2%2C0%2C3.3%2C1.5%2C5.1-1.1c1.1-1.7-1.1-5.8-3.6-3.2c-1.1%2C1.2-1.8%2C3.4-3.5%2C3.6%20c-8.2%2C1.1-20.1-5-20.9-13.8c2.5%2C2.3%2C6.8%2C4.2%2C10.2%2C5.5c1.4%2C0.5%2C2%2C2.4%2C4.1%2C1.9c1.4-0.3%2C1.9-2.3%2C0.4-3.7c-2.2-2.2-2%2C1.1-4.3%2C0.6%20c-3.4-0.8-7-2.5-9.2-5.8c6.2-1.4%2C14.9%2C0.8%2C21.4-0.2c2.6-0.4%2C3.6%2C1.6%2C5-0.4c1.5-2.1-0.3-4.6-1.6-4.5c-1.7%2C0.2-2.9%2C2.3-3%2C3.6%20c-7.2%2C1.5-14-1-22-0.2c1.2-2.3%2C4.3-4.3%2C7.5-4.8c1.7-0.3%2C1.9%2C1.5%2C3.6%2C0.4c0.4-0.3%2C0.7-0.6%2C0.9-1.1c0.4-1.1-0.2-2.3-1.3-2.6%20c-1.9-0.8-2.5%2C1.7-3.8%2C2.2c-2.4%2C1-5.1%2C2.5-7.2%2C2.4c4.1-5.8%2C8.5-10.3%2C13.9-12.5c1.8-0.7%2C2.8%2C1.6%2C5.1-0.2c0.8-0.6%2C0.8-3-0.9-4%20c-2.3-1.3-2.8%2C2.1-5.1%2C3.3c-6.5%2C3.2-11.9%2C8.4-15.4%2C14.8c-2.2%2C1.2-4.7-0.2-7%2C3.4c-11.3%2C4.7-24.5%2C7.4-36.4%2C8.7c4.8-2.4%2C8-5.8%2C11.3-9.3%20c0.7%2C0.2%2C2-0.5%2C3.9-3.4c7.2-0.4%2C15.1-1.1%2C19.3-9.3c1-2%2C5-4.4%2C3.6-6.9c-1.1-1.9-3.2-1.7-3.8-0.2c-1.4%2C3.4%2C0%2C5.3-0.7%2C6.6%20c-3.8%2C7.4-10.9%2C8.6-17.8%2C8.3c1.1-2%2C4.4-4.6%2C6.9-7.3c1.5-1.7%2C4.9-1%2C4.8-5.2c0-2.6-3.6-2.8-4.6-1.4c-1.4%2C1.9-0.4%2C4.3-1.4%2C5.4%20c-1.7%2C2.2-3.5%2C4.7-5.9%2C5.7c-0.8-6.4%2C1.2-12%2C4.1-17.2c1.3-2.5%2C6.8-1.7%2C5.6-6.3c-0.7-2.8-4.6-4.2-6-1.5c-1.1%2C2-0.3%2C5.6-1.1%2C7.5%20c-2.3%2C5.2-4.3%2C13.4-5.4%2C18c-2.3-3-3.7-6.7-4-10.5c-0.2-2.3%2C3-4.4%2C1.4-7.9c-1.1-2.5-6-2.1-6.1%2C3.6c0%2C3.2%2C2.5%2C3.3%2C3%2C5.2%20c0.9%2C4.1%2C4.2%2C8.2%2C5.1%2C11.2c-0.6%2C1.1-1.9%2C2.2-1.8%2C4.9c-7.1%2C10-18.5%2C11-31.9%2C7.6c-3.6-0.9-8.8-3.8-13.6-7.6%20c-2.7-5.1-5.5-9.3-10.5-11.5c12.2%2C4.7%2C29.2%2C13.5%2C44.7%2C14.8c-10.3-20.3-33-18.4-49.5-25.2c15.4%2C0.1%2C35.1-2.1%2C51.3-13%20c-18.3%2C0.3-35.4-3.6-56.8%2C7.8c-15.7-16.5-28.9-49.8-29.2-71.7C45.7%2C312.6%2C66.1%2C326%2C95.9%2C330c9.4%2C1.2%2C8.2%2C3.3%2C11.5%2C5.6%20c0.7-18.2-23.2-45.7-39.6-53.9c-3.6-1.8-7.7-2.5-11.7-3.4c-4.3-0.9-8.8-2-12.7-4.1c-4.4-2.3-7.7-6-9-10.9%20c-1.5-5.7-2.6-11.7-1.9-17.6c0.3-2.9%2C1.1-5.8%2C2.2-8.5c5.7-14.1%2C23.5-12.1%2C33-16.8c16.4-8.2%2C40.3-35.7%2C39.6-53.9%20c-3.2%2C2.3-2.1%2C4.3-11.5%2C5.6c-29.7%2C3.9-50.1%2C17.4-61.1%2C53.2c0.3-21.9%2C13.5-55.2%2C29.2-71.7c21.5%2C11.4%2C38.5%2C7.5%2C56.8%2C7.8%20c-16.2-10.8-35.9-13.1-51.3-13c16.5-6.8%2C39.2-5%2C49.5-25.2c-15.5%2C1.3-32.5%2C10-44.7%2C14.7c5-2.2%2C7.8-6.4%2C10.5-11.5%20c4.8-3.8%2C10-6.7%2C13.6-7.6c13.4-3.4%2C24.8-2.4%2C31.9%2C7.6c-0.1%2C2.7%2C1.2%2C3.8%2C1.8%2C4.9c-0.9%2C3-4.1%2C7.1-5.1%2C11.2c-0.5%2C1.9-3%2C2-3%2C5.2%20c0.1%2C5.7%2C5%2C6%2C6.1%2C3.6c1.6-3.5-1.6-5.6-1.4-7.9c0.3-3.8%2C1.7-7.4%2C4-10.5c1.1%2C4.6%2C3.1%2C12.8%2C5.4%2C18c0.8%2C1.9%2C0%2C5.5%2C1.1%2C7.5%20c1.5%2C2.7%2C5.3%2C1.3%2C6-1.5c1.2-4.5-4.2-3.8-5.6-6.3c-2.8-5.2-4.9-10.8-4.1-17.2c2.4%2C1.1%2C4.2%2C3.6%2C5.9%2C5.7c0.9%2C1.2%2C0%2C3.6%2C1.4%2C5.4%20c1%2C1.4%2C4.6%2C1.2%2C4.6-1.4c0.1-4.1-3.3-3.5-4.8-5.2c-2.5-2.7-5.8-5.3-6.9-7.3c6.8-0.3%2C14%2C0.9%2C17.8%2C8.3c0.6%2C1.3-0.7%2C3.1%2C0.7%2C6.6%20c0.6%2C1.5%2C2.7%2C1.7%2C3.8-0.2c1.5-2.5-2.6-4.9-3.6-6.9c-4.2-8.2-12.1-8.9-19.3-9.3c-1.8-3-3.1-3.6-3.9-3.4c-3.3-3.5-6.5-6.9-11.3-9.3%20c11.8%2C1.3%2C25.1%2C3.9%2C36.4%2C8.7c2.3%2C3.6%2C4.8%2C2.2%2C7%2C3.4c3.5%2C6.4%2C8.8%2C11.6%2C15.4%2C14.8c2.3%2C1.2%2C2.8%2C4.6%2C5.1%2C3.3c1.7-1%2C1.7-3.4%2C0.9-4%20c-2.3-1.8-3.3%2C0.6-5.1-0.2c-5.4-2.2-9.8-6.7-13.9-12.5c2.2-0.2%2C4.8%2C1.4%2C7.2%2C2.4c1.3%2C0.5%2C1.9%2C3.1%2C3.8%2C2.2c1.1-0.4%2C1.7-1.5%2C1.3-2.6%20c-0.2-0.5-0.5-0.8-0.9-1.1c-1.7-1.1-1.9%2C0.7-3.6%2C0.4c-3.2-0.5-6.3-2.5-7.5-4.8c7.9%2C0.7%2C14.7-1.7%2C22-0.2c0.1%2C1.3%2C1.3%2C3.4%2C3%2C3.6%20c1.3%2C0.1%2C3.1-2.4%2C1.6-4.5c-1.4-2-2.5%2C0-5-0.4c-6.4-1.1-15.2%2C1.2-21.4-0.2c2.2-3.2%2C5.8-4.9%2C9.2-5.8c2.3-0.6%2C2.1%2C2.7%2C4.3%2C0.6%20%20c1.5-1.5%2C0.9-3.4-0.4-3.7c-2.1-0.5-2.7%2C1.4-4.1%2C1.9c-3.5%2C1.3-7.7%2C3.2-10.2%2C5.5c0.8-8.9%2C12.6-15%2C20.9-13.8c1.7%2C0.2%2C2.4%2C2.4%2C3.5%2C3.6%20c2.4%2C2.6%2C4.7-1.6%2C3.6-3.2c-1.8-2.6-2.9-1-5.1-1.1c-7.8-3.8-22.1%2C5.2-24.9%2C13.6c-2.1%2C0.4-4.3-1.2-5.9%2C0.9%20c-12.6-4.9-25.8-7.9-39.2-9.1c4.4-2.7%2C9-5.1%2C13.8-7.2c2.3%2C0.5%2C3.8-0.8%2C5.1-1.4c4.3%2C2%2C7.5%2C6.3%2C12.5%2C7.5c1%2C0.2%2C1.8%2C2.4%2C3.9%2C2.2%20c2-0.2%2C2.5-3.5%2C1.4-4.7c-2.1-2.2-3.4%2C0-5.1%2C1.2c-4.5-1.7-8.6-4.4-11.8-7.9c0.6-1.1%2C1.4-2%2C2.4-2.8c3.3-2.6%2C8.2-3.4%2C11.8-3.6%20c1.5-0.1%2C3.8%2C1.8%2C5.6%2C1.6c1.8-0.2%2C2.4-3%2C0.7-4.5c-2-1.7-3.2%2C1.2-6.1%2C1.7c-5%2C0.5-9.9%2C1.9-14.4%2C4.1c1.7-4.2%2C7.5-10.5%2C12.4-13.1%20c1.5-0.8%2C2.8%2C1.7%2C5.7-0.8c0.5%2C0.3%2C1%2C0.6%2C1.5%2C0.9c11.3%2C5.7%2C34.9%2C6.9%2C42.8-1.9c-2.5-0.8-2.9%2C0.8-7.8-4.1%20c-15.6-15.5-31.1-21.1-52.4-10.8c10.1-10%2C31.3-17.7%2C46-16.2c4.8%2C17.7%2C14.5%2C25.8%2C22.8%2C36.6c-2.6-14.4-10.7-26.9-17.9-35.8%20c10.7%2C6.4%2C20.4%2C20.4%2C34.4%2C17.1c-7.8-8.4-19.7-14.3-27.5-19.2c6.4%2C3.7%2C12.4-1%2C21.3-1.1c6.6-0.1%2C14.3%2C6.6%2C22.4%2C6.2%20c16.4-0.9%2C27.7-12.3%2C35-25.8c-18.6%2C3.4-31.7-6.1-47.2%2C2.8c-7.8%2C4.5-11.5%2C19.7-24.7%2C15.1c10.1-4.4%2C15.2-11.2%2C21.5-17.2%20c3.8-3.6%2C8.4-6.1%2C10.9-10.9c-8.4%2C0.2-15.4%2C6.3-20.4%2C10.7c3.1-6.4%2C8.9-11%2C8.1-20c-7.6%2C6.8-16.7%2C17.3-20.6%2C26.8%20c-1.5%2C3.6-1.6%2C8.9-4.9%2C10.6c-6.7%2C3.4-12.4-2.3-22.6%2C1.5c6.8-11%2C20.9-11.8%2C27-22.7c4.8-8.5%2C5.8-17.6%2C7.5-28.2%20c-16.1%2C10.2-39.9%2C20.5-39.8%2C42.6c0%2C3.5%2C2.3%2C6.1%2C0.6%2C8.2c-4.6%2C0.5-9.1%2C1.4-13.5%2C2.6c8-12.8%2C12.4-28%2C11.9-46.3%20c-5.3%2C6.4-9%2C14.8-12.1%2C22.7c-0.2-4.5%2C0.9-9.9-0.8-13.5c-6.7%2C9.6-11.4%2C21.8-11%2C34.6c0.1%2C2.7%2C1.2%2C5.2%2C1.9%2C7.8%20c-6.5%2C4.1-12.5%2C8.9-17.9%2C14.3c6.3-16.9%2C5.3-35.2%2C0.9-51.8c-14.8%2C25.9-36.5%2C32.6-43%2C60.1c-3.3%2C13.9%2C7.6%2C33.1-9.6%2C44.7%20c5.6-16.5%2C4.1-29.3%2C4.5-42.9c0.3-8%2C2.3-16.1%2C0.2-23.8c-7.9%2C10.4-9%2C24.9-9.5%2C35.2c-3.1-10-1.8-21.5-11.2-29.2%20c-1%2C15.8%2C0.3%2C37.1%2C5.5%2C51.1c2%2C5.3%2C6.9%2C10.6%2C5.4%2C16.3c-3.2%2C11.4-14.3%2C12.8-20.4%2C28.9c-3.9-18.9%2C9-36.9%2C4.6-54.8%20C53.7%2C93.7%2C46%2C83.6%2C37.6%2C71.3c-5.9%2C29.5-19.2%2C68.4%2C1.9%2C89.7c3.4%2C3.4%2C8%2C3%2C8.4%2C7.2c-4%2C6.1-7.5%2C12.4-10.7%2C19%20c-4.3-22.7-15.7-43.5-32.4-59.5c0.9%2C12.6%2C5.3%2C25.3%2C9.8%2C36.8C10.2%2C160.4%2C6%2C153.8%2C1%2C152.4c2.6%2C17.5%2C9.6%2C35%2C22.2%2C47%20c2.6%2C2.5%2C6.2%2C3.6%2C9.2%2C5.3c-3.7%2C18.3-4.7%2C37.1-2.9%2C55.8c0.2%2C1.9%2C0.3%2C3.8%2C0.5%2C5.7c0.5%2C5.9%2C0.2%2C11.9%2C0.7%2C17.8c0.4%2C4.4%2C0.9%2C8.9%2C1.8%2C13.2%20c-3.1%2C1.8-6.6%2C2.8-9.2%2C5.3c-12.6%2C12-19.6%2C29.4-22.2%2C47c5-1.4%2C9.2-8%2C13.6-12.1c-4.6%2C11.6-8.9%2C24.2-9.8%2C36.8%20c16.8-16%2C28.1-36.8%2C32.4-59.5c3.1%2C6.5%2C6.7%2C12.9%2C10.7%2C19c-0.4%2C4.2-5%2C3.8-8.4%2C7.2c-21.1%2C21.2-7.8%2C60.2-1.9%2C89.7%20c8.4-12.3%2C16.1-22.4%2C19.5-36.5c4.4-17.9-8.5-35.9-4.6-54.8c6.2%2C16%2C17.3%2C17.4%2C20.4%2C28.9c1.6%2C5.7-3.4%2C10.9-5.4%2C16.3%20c-5.2%2C14-6.5%2C35.3-5.5%2C51.1c9.3-7.7%2C8.1-19.2%2C11.2-29.2c0.6%2C10.3%2C1.6%2C24.8%2C9.5%2C35.2c2.1-7.7%2C0.1-15.8-0.2-23.8%20c-0.4-13.5%2C1.1-26.4-4.5-42.9c17.2%2C11.6%2C6.3%2C30.9%2C9.6%2C44.7c6.6%2C27.5%2C28.2%2C34.2%2C43%2C60.1c4.4-16.6%2C5.4-34.9-0.9-51.8%20c5.4%2C5.4%2C11.4%2C10.2%2C17.9%2C14.3c-0.6%2C2.6-1.8%2C5.1-1.9%2C7.8c-0.4%2C12.9%2C4.3%2C25%2C11%2C34.6c1.7-3.6%2C0.6-9%2C0.8-13.5c3.1%2C8%2C6.8%2C16.4%2C12.1%2C22.7%20c0.4-18.3-3.9-33.5-11.9-46.3c4.4%2C1.2%2C9%2C2.1%2C13.5%2C2.6c1.7%2C2.2-0.6%2C4.7-0.6%2C8.2c-0.1%2C22.1%2C23.7%2C32.4%2C39.8%2C42.6%20c-1.7-10.6-2.7-19.7-7.5-28.2c-6.1-10.8-20.2-11.6-27-22.7c10.1%2C3.8%2C15.9-2%2C22.6%2C1.5c3.3%2C1.7%2C3.4%2C7%2C4.9%2C10.6%20c3.9%2C9.5%2C13%2C20.1%2C20.6%2C26.8c0.8-9-5-13.6-8.1-20c4.9%2C4.4%2C12%2C10.5%2C20.4%2C10.7c-2.5-4.8-7.1-7.4-10.9-10.9c-6.3-6-11.5-12.8-21.5-17.2%20c13.3-4.6%2C16.9%2C10.6%2C24.7%2C15.1c15.5%2C8.9%2C28.6-0.6%2C47.2%2C2.8C268.7%2C456.1%2C257.4%2C444.7%2C241%2C443.8z%20M112.2%2C375.6%20c-11.5-5.4-28.3-5.5-35.9-16.3C88.9%2C360.6%2C104.9%2C363.5%2C112.2%2C375.6z%20M92.8%2C313.8c-14.4-12.4-35-16.2-45.8-33.4%20C63.8%2C283.1%2C84.7%2C297.1%2C92.8%2C313.8z%20M100%2C327.5c-21-5.3-46-8.2-56.8-41.4C61.6%2C300.4%2C85.5%2C308.2%2C100%2C327.5z%20M47%2C221.6%20c10.7-17.2%2C31.3-21%2C45.8-33.4C84.7%2C204.9%2C63.8%2C218.9%2C47%2C221.6z%20M100%2C174.5c-14.5%2C19.4-38.4%2C27.1-56.8%2C41.4%20C54.1%2C182.7%2C79%2C179.8%2C100%2C174.5z%20M112.2%2C126.4c-7.3%2C12.1-23.3%2C14.9-35.9%2C16.3C83.9%2C132%2C100.7%2C131.9%2C112.2%2C126.4z%20M265.6%2C37%20c-14%2C7.9-29.6%2C4.5-40.8%2C7C230.8%2C34.5%2C255.6%2C32.4%2C265.6%2C37z%20M264.8%2C41.8L264.8%2C41.8c-7.3%2C13.1-32.2%2C15.8-40.3%2C6.1%20C238.8%2C44.1%2C249.3%2C47.9%2C264.8%2C41.8z%20M230.6%2C25.5L230.6%2C25.5c-7.7%2C7.4-13.5%2C16.1-24.6%2C21.2C212.8%2C38.8%2C219.2%2C30.5%2C230.6%2C25.5z%20M222.5%2C17.2L222.5%2C17.2c-3.3%2C10.8-12.1%2C18.1-17.7%2C27.5C206%2C34.9%2C214%2C24.3%2C222.5%2C17.2z%20M220.2%2C69.8c-8.9%2C1.4-17.6-6.6-24-13.3%20C204.5%2C56%2C212.4%2C65.7%2C220.2%2C69.8z%20M205.6%2C84.9c-6-6.9-14.4-15.7-18.4-29.5C195.3%2C61.2%2C201%2C71.9%2C205.6%2C84.9z%20M178.4%2C44.3%20c1.9-13.6%2C20.5-21.5%2C28.6-33.1C204%2C26.6%2C194.4%2C37.6%2C178.4%2C44.3z%20M204.2%2C8.3c-2%2C6.6-11.2%2C11.6-17.4%2C17.5c-6.3%2C5.9-9.7%2C13.8-12.5%2C21.3%20C169.8%2C27.1%2C189.6%2C17.7%2C204.2%2C8.3z%20M167.8%2C13.9L167.8%2C13.9c1.3%2C15.5-5.1%2C31.1-13.6%2C42.3C154.1%2C41.5%2C161.5%2C26.4%2C167.8%2C13.9z%20M155.8%2C22.9L155.8%2C22.9c1.9%2C12.1-2.5%2C25.2-5.6%2C36C143.4%2C46%2C153.1%2C33.6%2C155.8%2C22.9z%20M192.8%2C85L192.8%2C85%20c-15.5%2C0.5-30.1-9.8-45.1-13.8C167.7%2C62.1%2C180.6%2C75.2%2C192.8%2C85z%20M183.2%2C87.1c-11.3%2C3-27.3-2.7-36.3-11.1h0%20C159.6%2C74.3%2C170.8%2C84.5%2C183.2%2C87.1z%20M95.5%2C100c10.3-21.2%2C24-30.3%2C33.3-55C134.2%2C66.7%2C112.6%2C99.6%2C95.5%2C100z%20M125.1%2C39.3%20c-6%2C24.6-24.4%2C40.4-32.9%2C56.4C89%2C79.2%2C111%2C47%2C125.1%2C39.3z%20M93.8%2C111L93.8%2C111c6.3-8.1%2C20.1-10.9%2C27.5-21.2c1.7-2.3%2C3.2-4.7%2C4.5-7.2%20c2-3%2C4.8-5.3%2C8.2-6.6c6.7-2.4%2C12.4%2C3.2%2C17.6%2C7.5c-0.8%2C0.7-1.2%2C1.9-2.7%2C2.8c-5.7%2C3.6-10.9%2C8.9-13.4%2C14.2c-1.3-3.4-1.5-6.6-0.5-8.8%20c1-2.1%2C3.1-2.2%2C1.9-4.9c-0.6-1.3-2.7-0.9-3.2%2C0c-0.9%2C1.5-0.1%2C3%2C0%2C5c0.1%2C3.6-0.5%2C5.8%2C0.7%2C10.3c-1.5%2C0.8-3%2C1.4-3.9%2C3.1%20c-7%2C4.2-9.6%2C6.1-15.1%2C8.1c-4.6%2C1.7-12.9%2C1.9-19.8%2C4.5c-3.3%2C1.4-6.5%2C3.2-9.4%2C5.4C88.4%2C119%2C90.9%2C114.9%2C93.8%2C111z%20M80.2%2C70.8%20c-0.5%2C16.6%2C2.2%2C32-3.8%2C50.5C75.6%2C105.2%2C73.9%2C89.5%2C80.2%2C70.8z%20M64.6%2C72.5c7.1%2C14.6%2C5.5%2C32.3%2C8.9%2C48.3C65.3%2C109.7%2C63%2C89.7%2C64.6%2C72.5z%20M112.4%2C158.9L112.4%2C158.9c-12.3%2C0.5-28.8%2C2.2-45.9-6.2C80%2C148.3%2C95.6%2C151.8%2C112.4%2C158.9z%20M38.3%2C86.1L38.3%2C86.1%20c4.3%2C8.9%2C0.2%2C24.8-0.3%2C38.1c-0.5%2C13.4%2C3.7%2C25.2%2C8.2%2C35.9C22.8%2C146.1%2C33.2%2C113%2C38.3%2C86.1z%20M47.5%2C152.4L47.5%2C152.4%20c-11.1-15.5-0.5-45.8-3.7-66.8C55.6%2C104.1%2C56.7%2C126.4%2C47.5%2C152.4z%20M8.5%2C135.8c16%2C13.5%2C24.6%2C36.5%2C27%2C57.5%20C21.3%2C179.2%2C14.2%2C155.7%2C8.5%2C135.8z%20M5.4%2C159.1c13.3%2C9.4%2C21.5%2C27.5%2C28.8%2C41.8C15.3%2C196.7%2C12.9%2C172.8%2C5.4%2C159.1z%20M5.4%2C342.9%20c7.6-13.6%2C9.9-37.6%2C28.8-41.8C26.9%2C315.3%2C18.6%2C333.4%2C5.4%2C342.9z%20M8.5%2C366.2c5.7-19.9%2C12.8-43.4%2C27-57.5%20C33.1%2C329.7%2C24.5%2C352.8%2C8.5%2C366.2z%20M38.3%2C415.9c-5.2-27-15.5-60.1%2C7.9-74c-4.5%2C10.6-8.7%2C22.5-8.2%2C35.9%20C38.6%2C391.1%2C42.6%2C407.1%2C38.3%2C415.9z%20M43.8%2C416.4c3.2-21-7.4-51.3%2C3.7-66.8l0%2C0C56.7%2C375.6%2C55.6%2C397.9%2C43.8%2C416.4z%20M66.5%2C349.3%20c17.1-8.4%2C33.5-6.8%2C45.9-6.2l0%2C0C95.6%2C350.2%2C80%2C353.7%2C66.5%2C349.3z%20M64.6%2C429.5c-1.6-17.2%2C0.8-37.2%2C8.9-48.3%20C70%2C397.1%2C71.6%2C414.9%2C64.6%2C429.5z%20M80.2%2C431.2c-6.3-18.7-4.6-34.4-3.8-50.5C82.4%2C399.3%2C79.7%2C414.7%2C80.2%2C431.2z%20M192.8%2C417L192.8%2C417%20c-12.1%2C9.8-25%2C22.9-45.1%2C13.8C162.7%2C426.8%2C177.2%2C416.5%2C192.8%2C417z%20M183.2%2C414.9c-12.3%2C2.6-23.6%2C12.9-36.3%2C11.1l0%2C0%20C155.8%2C417.5%2C171.8%2C411.8%2C183.2%2C414.9z%20M92.2%2C406.3c8.5%2C16%2C26.8%2C31.8%2C32.9%2C56.4C111%2C455%2C89%2C422.8%2C92.2%2C406.3z%20M128.8%2C456.9%20c-9.3-24.7-23-33.7-33.3-55C112.6%2C402.4%2C134.2%2C435.3%2C128.8%2C456.9z%20M134%2C425.9c-3.4-1.3-6.2-3.6-8.2-6.6c-1.3-2.5-2.9-4.9-4.5-7.2%20c-7.4-10.3-21.3-13-27.5-21.2h0c-2.9-3.9-5.4-8-7.5-12.4c2.9%2C2.2%2C6%2C4%2C9.4%2C5.4c6.8%2C2.6%2C15.2%2C2.9%2C19.8%2C4.5c5.5%2C2%2C8.1%2C4%2C15.1%2C8.1%20c0.9%2C1.7%2C2.4%2C2.3%2C3.9%2C3.1c-1.2%2C4.6-0.6%2C6.8-0.7%2C10.3c-0.1%2C2-0.8%2C3.4%2C0%2C5c0.5%2C0.9%2C2.6%2C1.3%2C3.2%2C0c1.2-2.8-0.9-2.8-1.9-4.9%20c-1-2.1-0.8-5.4%2C0.5-8.8c2.4%2C5.3%2C7.7%2C10.6%2C13.4%2C14.2c1.5%2C0.9%2C1.9%2C2.1%2C2.7%2C2.8C146.5%2C422.8%2C140.7%2C428.3%2C134%2C425.9z%20M155.8%2C479.1%20c-2.7-10.7-12.4-23.1-5.6-36C153.3%2C453.9%2C157.7%2C467%2C155.8%2C479.1z%20M167.8%2C488.1c-6.4-12.5-13.7-27.5-13.6-42.3%20C162.7%2C456.9%2C169.2%2C472.6%2C167.8%2C488.1z%20M207%2C490.7c-8-11.6-26.7-19.4-28.6-33.1C194.4%2C464.4%2C204%2C475.4%2C207%2C490.7z%20M186.8%2C476.3%20c6.3%2C5.9%2C15.4%2C10.9%2C17.4%2C17.5c-14.6-9.5-34.4-18.8-29.9-38.8C177.1%2C462.5%2C180.5%2C470.4%2C186.8%2C476.3z%20M205.6%2C417.1%20c-4.5%2C13-10.2%2C23.7-18.4%2C29.5C191.2%2C432.8%2C199.6%2C424%2C205.6%2C417.1z%20M196.1%2C445.5c6.5-6.7%2C15.2-14.7%2C24-13.3l0%2C0%20C212.4%2C436.3%2C204.5%2C446%2C196.1%2C445.5z%20M230.6%2C476.5L230.6%2C476.5c-11.4-5-17.7-13.3-24.6-21.2C217.1%2C460.5%2C222.8%2C469.1%2C230.6%2C476.5z%20M222.5%2C484.8L222.5%2C484.8c-8.5-7.1-16.5-17.7-17.7-27.5C210.4%2C466.6%2C219.2%2C473.9%2C222.5%2C484.8z%20M264.8%2C460.1L264.8%2C460.1%20c-15.5-6-26-2.2-40.3-6.1C232.6%2C444.3%2C257.4%2C447%2C264.8%2C460.1z%20M224.8%2C458c11.2%2C2.5%2C26.8-0.9%2C40.8%2C7%20C255.6%2C469.6%2C230.8%2C467.5%2C224.8%2C458z'/%3E%3C/svg%3E");
  background-size: contain;
  -khtml-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
.hero-wrapper::after {
  left: auto;
  right: 0;
  -webkit-transform: translateY(-50%) rotate(180deg);
  -moz-transform: translateY(-50%) rotate(180deg);
  -ms-transform: translateY(-50%) rotate(180deg);
  -o-transform: translateY(-50%) rotate(180deg);
  transform: translateY(-50%) rotate(180deg);
}
.hero-wrapper.x-pad {
  padding: 150px 0;
}
.hero-wrapper h2 {
  color: #fff;
  font-size: 18pt;
  text-transform: uppercase;
  margin-bottom: 15px;
}
.hero-wrapper h1.hero-title {
  padding-bottom: 3px;
  margin-bottom: 40px;
}
.hero-wrapper h1.hero-title:after {
  content: "";
  height: 1px;
  width: 100px;
  position: absolute;
  bottom: -34px;
  left: 50%;
  margin-left: -50px;
  background-color: #fff;
}
.hero-wrapper .hero-subtitle {
  font-style: normal;
  font-size: 18pt;
}
.hero-wrapper .hero-subtitle span {
  font-family: "Playfair Display", serif;
  font-size: 50pt;
  position: relative;
  top: 10px;
  margin: 0 14px;
}
.hero-wrapper .btn {
  margin-top: 40px;
}

.hero-divider-top,
.hero-divider-bottom {
  position: relative;
  width: 100%;
  height: 34px;
  margin: 40px auto;
}
.hero-divider-top::before,
.hero-divider-bottom::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 34px;
  background-position-x: center;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 126.17 16.56'%3E%3Cpath fill='%238eaeba' d='M3.62,14.25l0,.17a.7.7,0,0,1-.24.48.85.85,0,0,1-.55.24H2.74a1,1,0,0,1-.61-.46,1.59,1.59,0,0,1-.25-.86l0-.21a3.18,3.18,0,0,1,1.26-1.86c1.8-1.47,4.29-2,6.74-2.11q1.11-.07,2.22-.07C18,9.55,23.85,11,29.75,12.38s11.8,2.86,17.79,2.86a33.3,33.3,0,0,0,4.8-.34,14,14,0,0,0,5.63-2A6.33,6.33,0,0,0,61,8.15c0-.11,0-.21,0-.32A3.85,3.85,0,0,0,60.6,6a2.42,2.42,0,0,0-1.49-1.22,2.25,2.25,0,0,0-.58-.07,2.89,2.89,0,0,0-1.63.56,5.33,5.33,0,0,0-1.2,1.19h0a8.46,8.46,0,0,0-1.65,4.94,6.14,6.14,0,0,0,1.53,4.2,7.76,7.76,0,0,0,5.26,2.25l.72,0a12.33,12.33,0,0,0,8.35-3.48,12.07,12.07,0,0,0,3.9-8.16V6a5.77,5.77,0,0,0-.72-2.81,3.34,3.34,0,0,0-.94-1.06,2.36,2.36,0,0,0-1.37-.44h-.25a2.8,2.8,0,0,0-2,1.29,6.84,6.84,0,0,0-.94,2.16A16.39,16.39,0,0,0,67,9.28a9.88,9.88,0,0,0,.38,2.79,7.59,7.59,0,0,0,1.8,3.12,5.86,5.86,0,0,0,3.14,1.74,5.54,5.54,0,0,0,1,.09,7.06,7.06,0,0,0,4.19-1.56,14.78,14.78,0,0,0,3.15-3.36A6.18,6.18,0,0,0,82,8.85V8.76a3.11,3.11,0,0,0-.59-1.81,2.29,2.29,0,0,0-1.66-1h-.17a2.45,2.45,0,0,0-1.43.5,3.47,3.47,0,0,0-1,1.11,4.32,4.32,0,0,0-.55,2.13A5.84,5.84,0,0,0,77.72,13a6.81,6.81,0,0,0,2.68,2.29,10.63,10.63,0,0,0,4.54.89,29.17,29.17,0,0,0,4.16-.37C99.25,14.31,109.32,12,119.44,12q1.12,0,2.24,0a10,10,0,0,1,2.14.25,3.3,3.3,0,0,1,1.65.91,2.2,2.2,0,0,1,.57,1.52,2.81,2.81,0,0,1-.61,1.74,1.9,1.9,0,0,1-1.48.75h-.22l-.06.5.1-.49a2.07,2.07,0,0,1-1-.49.76.76,0,0,1-.25-.54.71.71,0,0,1,.11-.37.5.5,0,0,0-.85-.53,1.71,1.71,0,0,0-.26.9,1.75,1.75,0,0,0,.55,1.25,3.05,3.05,0,0,0,1.54.76h0l.34,0a2.9,2.9,0,0,0,2.25-1.12,3.79,3.79,0,0,0,.83-2.37,3.2,3.2,0,0,0-.83-2.2,4.28,4.28,0,0,0-2.13-1.2,10.93,10.93,0,0,0-2.36-.29q-1.14,0-2.28,0c-10.28,0-20.39,2.28-30.49,3.74a28.43,28.43,0,0,1-4,.36,9.66,9.66,0,0,1-4.12-.8,5.82,5.82,0,0,1-2.28-2,4.84,4.84,0,0,1-.92-2.7A3.31,3.31,0,0,1,78,8.06a2.5,2.5,0,0,1,.69-.79A1.45,1.45,0,0,1,79.58,7h.09a1.3,1.3,0,0,1,.93.57A2.12,2.12,0,0,1,81,8.76v.06a5.27,5.27,0,0,1-1.17,2.71,13.79,13.79,0,0,1-2.93,3.13A6.07,6.07,0,0,1,73.31,16a4.54,4.54,0,0,1-.82-.07,4.86,4.86,0,0,1-2.6-1.45,6.61,6.61,0,0,1-1.56-2.71A8.9,8.9,0,0,1,68,9.28a15.4,15.4,0,0,1,.57-3.88,5.88,5.88,0,0,1,.79-1.84,1.8,1.8,0,0,1,1.29-.87h.15a1.35,1.35,0,0,1,.8.26,2.77,2.77,0,0,1,.9,1.27A5,5,0,0,1,72.82,6v.18a11.08,11.08,0,0,1-3.58,7.47,11.34,11.34,0,0,1-7.66,3.21l-.65,0a6.78,6.78,0,0,1-4.59-1.92,5.14,5.14,0,0,1-1.27-3.53A7.47,7.47,0,0,1,56.51,7a4.28,4.28,0,0,1,1-1,1.9,1.9,0,0,1,1.06-.39,1.23,1.23,0,0,1,.32,0,1.42,1.42,0,0,1,.87.73,2.85,2.85,0,0,1,.33,1.35v.24a5.33,5.33,0,0,1-2.64,4,13.12,13.12,0,0,1-5.22,1.87,32.34,32.34,0,0,1-4.65.32c-5.83,0-11.67-1.41-17.56-2.83S18.15,8.55,12.11,8.55q-1.14,0-2.28.07A12.7,12.7,0,0,0,2.52,11,4.13,4.13,0,0,0,.9,13.44a2.35,2.35,0,0,0,0,.36,2.59,2.59,0,0,0,.42,1.41,2,2,0,0,0,1.22.88,1.58,1.58,0,0,0,.35,0,1.83,1.83,0,0,0,1.24-.51,1.68,1.68,0,0,0,.56-1.21,1.36,1.36,0,0,0-.14-.61.5.5,0,1,0-.9.44Z' transform='translate(-0.88 -1.69)'/%3E%3C/svg%3E");
}
.hero-divider-top.light::before,
.hero-divider-bottom.light::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 126.17 16.56'%3E%3Cpath fill='%23fff' d='M3.62,14.25l0,.17a.7.7,0,0,1-.24.48.85.85,0,0,1-.55.24H2.74a1,1,0,0,1-.61-.46,1.59,1.59,0,0,1-.25-.86l0-.21a3.18,3.18,0,0,1,1.26-1.86c1.8-1.47,4.29-2,6.74-2.11q1.11-.07,2.22-.07C18,9.55,23.85,11,29.75,12.38s11.8,2.86,17.79,2.86a33.3,33.3,0,0,0,4.8-.34,14,14,0,0,0,5.63-2A6.33,6.33,0,0,0,61,8.15c0-.11,0-.21,0-.32A3.85,3.85,0,0,0,60.6,6a2.42,2.42,0,0,0-1.49-1.22,2.25,2.25,0,0,0-.58-.07,2.89,2.89,0,0,0-1.63.56,5.33,5.33,0,0,0-1.2,1.19h0a8.46,8.46,0,0,0-1.65,4.94,6.14,6.14,0,0,0,1.53,4.2,7.76,7.76,0,0,0,5.26,2.25l.72,0a12.33,12.33,0,0,0,8.35-3.48,12.07,12.07,0,0,0,3.9-8.16V6a5.77,5.77,0,0,0-.72-2.81,3.34,3.34,0,0,0-.94-1.06,2.36,2.36,0,0,0-1.37-.44h-.25a2.8,2.8,0,0,0-2,1.29,6.84,6.84,0,0,0-.94,2.16A16.39,16.39,0,0,0,67,9.28a9.88,9.88,0,0,0,.38,2.79,7.59,7.59,0,0,0,1.8,3.12,5.86,5.86,0,0,0,3.14,1.74,5.54,5.54,0,0,0,1,.09,7.06,7.06,0,0,0,4.19-1.56,14.78,14.78,0,0,0,3.15-3.36A6.18,6.18,0,0,0,82,8.85V8.76a3.11,3.11,0,0,0-.59-1.81,2.29,2.29,0,0,0-1.66-1h-.17a2.45,2.45,0,0,0-1.43.5,3.47,3.47,0,0,0-1,1.11,4.32,4.32,0,0,0-.55,2.13A5.84,5.84,0,0,0,77.72,13a6.81,6.81,0,0,0,2.68,2.29,10.63,10.63,0,0,0,4.54.89,29.17,29.17,0,0,0,4.16-.37C99.25,14.31,109.32,12,119.44,12q1.12,0,2.24,0a10,10,0,0,1,2.14.25,3.3,3.3,0,0,1,1.65.91,2.2,2.2,0,0,1,.57,1.52,2.81,2.81,0,0,1-.61,1.74,1.9,1.9,0,0,1-1.48.75h-.22l-.06.5.1-.49a2.07,2.07,0,0,1-1-.49.76.76,0,0,1-.25-.54.71.71,0,0,1,.11-.37.5.5,0,0,0-.85-.53,1.71,1.71,0,0,0-.26.9,1.75,1.75,0,0,0,.55,1.25,3.05,3.05,0,0,0,1.54.76h0l.34,0a2.9,2.9,0,0,0,2.25-1.12,3.79,3.79,0,0,0,.83-2.37,3.2,3.2,0,0,0-.83-2.2,4.28,4.28,0,0,0-2.13-1.2,10.93,10.93,0,0,0-2.36-.29q-1.14,0-2.28,0c-10.28,0-20.39,2.28-30.49,3.74a28.43,28.43,0,0,1-4,.36,9.66,9.66,0,0,1-4.12-.8,5.82,5.82,0,0,1-2.28-2,4.84,4.84,0,0,1-.92-2.7A3.31,3.31,0,0,1,78,8.06a2.5,2.5,0,0,1,.69-.79A1.45,1.45,0,0,1,79.58,7h.09a1.3,1.3,0,0,1,.93.57A2.12,2.12,0,0,1,81,8.76v.06a5.27,5.27,0,0,1-1.17,2.71,13.79,13.79,0,0,1-2.93,3.13A6.07,6.07,0,0,1,73.31,16a4.54,4.54,0,0,1-.82-.07,4.86,4.86,0,0,1-2.6-1.45,6.61,6.61,0,0,1-1.56-2.71A8.9,8.9,0,0,1,68,9.28a15.4,15.4,0,0,1,.57-3.88,5.88,5.88,0,0,1,.79-1.84,1.8,1.8,0,0,1,1.29-.87h.15a1.35,1.35,0,0,1,.8.26,2.77,2.77,0,0,1,.9,1.27A5,5,0,0,1,72.82,6v.18a11.08,11.08,0,0,1-3.58,7.47,11.34,11.34,0,0,1-7.66,3.21l-.65,0a6.78,6.78,0,0,1-4.59-1.92,5.14,5.14,0,0,1-1.27-3.53A7.47,7.47,0,0,1,56.51,7a4.28,4.28,0,0,1,1-1,1.9,1.9,0,0,1,1.06-.39,1.23,1.23,0,0,1,.32,0,1.42,1.42,0,0,1,.87.73,2.85,2.85,0,0,1,.33,1.35v.24a5.33,5.33,0,0,1-2.64,4,13.12,13.12,0,0,1-5.22,1.87,32.34,32.34,0,0,1-4.65.32c-5.83,0-11.67-1.41-17.56-2.83S18.15,8.55,12.11,8.55q-1.14,0-2.28.07A12.7,12.7,0,0,0,2.52,11,4.13,4.13,0,0,0,.9,13.44a2.35,2.35,0,0,0,0,.36,2.59,2.59,0,0,0,.42,1.41,2,2,0,0,0,1.22.88,1.58,1.58,0,0,0,.35,0,1.83,1.83,0,0,0,1.24-.51,1.68,1.68,0,0,0,.56-1.21,1.36,1.36,0,0,0-.14-.61.5.5,0,1,0-.9.44Z' transform='translate(-0.88 -1.69)'/%3E%3C/svg%3E");
}

.hero-divider-bottom::before {
  -webkit-transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -ms-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
}

.hero-title {
  color: #8eaeba;
  font-family: "Playfair Display", serif;
  font-size: 74pt;
  margin-bottom: 5px;
  position: relative;
  min-height: 55px;
  line-height: 1;
}
.hero-title small {
  font-size: 36pt;
  display: inline-block;
}
.hero-title span {
  display: inline-block;
}
.hero-title.light {
  color: #fff;
}

.hero-subtitle {
  color: #8eaeba;
  font-size: 16pt;
  font-family: "Poppins", sans-serif;
  font-style: italic;
  font-weight: 300;
}
.hero-subtitle.light {
  color: #fff;
}

/* Hero Slider ---- */
.zs-enabled {
  position: relative;
}

.zs-enabled .zs-slideshow,
.zs-enabled .zs-slides,
.zs-enabled .zs-slide {
  position: absolute;
  z-index: -2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: block;
  padding: 0;
}

.zs-enabled .zs-slideshow,
.zs-enabled .zs-slides {
  position: fixed;
  max-height: 100vh;
}

.zs-enabled .zs-slideshow .zs-slides .zs-slide {
  background: transparent none no-repeat 50% 50%;
  background-size: cover;
  position: absolute;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1.2, 1.2);
  -moz-transform: scale(1.2, 1.2);
  -ms-transform: scale(1.2, 1.2);
  -o-transform: scale(1.2, 1.2);
  transform: scale(1.2, 1.2);
  filter: grayscale(1%);
  /*Fix flickering on Firefox*/
}

.zs-enabled .zs-slideshow .zs-slides .zs-slide.active {
  visibility: visible;
  opacity: 1;
}

.zs-enabled .zs-slideshow .zs-bullets {
  position: absolute;
  z-index: 4;
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
}

.zs-enabled .zs-slideshow .zs-bullets .zs-bullet {
  display: inline-block;
  cursor: pointer;
  border: 2px solid #ccc;
  width: 14px;
  height: 14px;
  border-radius: 8px;
  margin: 10px;
  background-color: #4a4a4a;
}

.zs-enabled .zs-slideshow .zs-bullets .zs-bullet.active {
  background-color: #ccc;
}

.zs-enabled .zs-slideshow:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background: transparent none repeat 0 0;
}

.countdown {
  margin: 30px auto 50px;
  min-height: 59px;
}
.countdown > div {
  display: inline-block;
  color: #fff;
  text-align: center;
  line-height: 1;
  padding: 0 22px;
  border-right: 1px solid rgba(255, 255, 255, 0.5);
}
.countdown > div:last-child {
  border-right: none;
}
.countdown > div > span {
  font-size: 12pt;
  -khtml-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
}
.countdown > div > div {
  font-size: 55pt;
}
.countdown .end {
  font-size: 24pt;
}

.free-wall {
  display: block;
  position: fixed;
  left: 0;
  top: 56px;
  overflow: hidden;
  z-index: -999;
  height: 100%;
  width: 100%;
  padding: 0;
  max-width: none;
  -webkit-backface-visibility: hidden;
}
.free-wall .item {
  background-color: #FFFFFF;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  cursor: default;
  float: left;
  overflow: hidden;
  height: 33.3333%;
}
.free-wall .item img {
  height: 100%;
  width: 100%;
  visibility: hidden;
}
.free-wall .item[data-state=init] {
  display: none;
}
.free-wall .item[data-state=start] {
  display: block;
  animation: start 0.5s;
  -webkit-animation: start 0.5s;
}
.free-wall .item[data-state=move] {
  transition: top 0.5s, left 0.5s, width 0.5s, height 0.5s;
  -webkit-transition: top 0.5s, left 0.5s, width 0.5s, height 0.5s;
}

.background-video {
  position: fixed;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -100;
  transform: translateX(-50%) translateY(-50%);
  background-size: cover;
  transition: 1s opacity;
}

/* #ABOUT US
================================================== */
#about-us {
  margin-top: -210px;
  background: linear-gradient(to bottom, #fff, #fff) no-repeat 0px 210px;
  position: relative;
}

.about-elems-wrapper {
  position: relative;
}

.element {
  position: relative;
  margin-bottom: 40px;
}
.element .highlight {
  display: block;
  width: 100%;
  text-align: center;
  color: #fff;
  margin-bottom: 30px;
  font-family: "Playfair Display", serif;
}
.element .highlight i {
  -khtml-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.element .image {
  position: relative;
  width: 100%;
  overflow: hidden;
  -webkit-box-shadow: inset 0px 0px 0px 15px #fff;
  -moz-box-shadow: inset 0px 0px 0px 15px #fff;
  box-shadow: inset 0px 0px 0px 15px #fff;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.element .image::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.element .image img {
  width: 100%;
  height: auto;
  z-index: -1;
  position: relative;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.element .image .hover-info {
  --offset: 10px;
  --border-size: 1px;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border: 36px solid transparent;
  background-color: rgba(255, 255, 255, 0.85);
  text-align: left;
  z-index: 1;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.element .image .hover-info .h-lines {
  transform: scaleX(0);
  border-top-color: #8eaeba;
  border-bottom-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.element .image .hover-info .v-lines {
  transform: scaleY(0);
  border-left-color: #8eaeba;
  border-right-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.element .image .hover-info .content {
  width: 100%;
  height: auto;
  padding: 15px;
  overflow: auto;
  position: relative;
  z-index: 1;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
.element .image .hover-info .content p {
  width: 100%;
  margin-bottom: 20px;
}
.element .image .hover-info .sn-icons {
  float: none;
  position: relative;
  margin: 0;
  padding: 0;
  text-align: center;
  width: 100%;
}
.element .image .hover-info .sn-icons a {
  color: #8eaeba;
}
.element .image .hover-info h3 {
  margin-bottom: 33px;
  position: relative;
  font-family: "Playfair Display", serif;
  font-size: 30pt;
}
.element .image .hover-info h3 small {
  display: block;
  font-size: 12pt;
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  text-transform: uppercase;
  margin-top: 6px;
}
.element .image:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.element .image:hover .hover-info {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.element .image:hover .hover-info .h-lines {
  transform: scaleX(1);
}
.element .image:hover .hover-info .v-lines {
  transform: scaleY(1);
}
.element .image:hover img {
  webkit-filter: blur(4px) grayscale(80%) brightness(1.4);
  filter: blur(4px) grayscale(80%) brightness(1.4);
}

.divider-about-us {
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -52.5px;
  color: #8eaeba;
  background-color: #fff;
  width: 105px;
  height: 105px;
  line-height: 105px;
  text-align: center;
  z-index: 2;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
}
.divider-about-us i {
  display: inline-block;
  color: #8eaeba;
  font-size: 44pt;
  -webkit-transform: rotate(360deg);
  -moz-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  transform: rotate(360deg);
  -moz-transition: transform 0.3s ease;
  -o-transition: transform 0.3s ease;
  -webkit-transition: transform 0.3s ease;
  transition: transform 0.3s ease;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.divider-about-us i::before {
  line-height: 105px;
}
.divider-about-us.flip i {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}

.about-us-desc {
  text-align: center;
}
.about-us-desc h3 {
  font-family: "Playfair Display", serif;
  margin-bottom: 40px;
  font-weight: 700;
}
.about-us-desc h3 small {
  display: block;
  font-weight: 400;
  font-size: 18pt;
}

.neela-quote {
  position: relative;
  padding: 46px 0;
  font-style: italic;
  font-size: 22pt;
  font-weight: 300;
  text-align: center;
  margin-top: 1rem;
}
.neela-quote::before, .neela-quote::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 24px;
  background-position-x: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='854' height='26' viewBox='0 0 854 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23000' stroke-width='1.5px' d='M417.19,2.362L427.98,13,417.19,23.634,406.4,13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23000' stroke-width='1.5px' d='M425.985,2.362L436.775,13l-10.79,10.636L415.2,13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23000' stroke-width='1.5px' d='M434.78,2.362L445.57,13,434.78,23.634,423.991,13Z'/%3E%3Cpath fill-rule='evenodd' fill='%23000' d='M451,14V13H854v1H451Z'/%3E%3Cpath fill-rule='evenodd' fill='%23000' d='M0,14V13H400v1H0Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  -khtml-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
}
.neela-quote::after {
  top: auto;
  bottom: 0;
}
.neela-quote.light {
  color: #fff;
}
.neela-quote.light::before, .neela-quote.light::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='854' height='26' viewBox='0 0 854 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M417.19,2.362L427.98,13,417.19,23.634,406.4,13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M425.985,2.362L436.775,13l-10.79,10.636L415.2,13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M434.78,2.362L445.57,13,434.78,23.634,423.991,13Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M451,14V13H854v1H451Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M0,14V13H400v1H0Z'/%3E%3C/svg%3E");
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.neela-quote.small {
  font-size: 16pt;
}

#about-us-quote-section {
  background-color: #f9f9f9;
}

.element-v2 {
  position: relative;
  margin-top: 50px;
  margin-bottom: 100px;
  width: 100%;
  display: table;
  table-layout: fixed;
}
.element-v2 .image {
  width: 40%;
  height: 100%;
  display: table-cell;
  float: none;
  vertical-align: top;
  padding: 0;
  margin: 0;
  min-height: 0;
  background-clip: border-box;
  background-origin: padding-box;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}
.element-v2 .info {
  --offset: 12px;
  --border-size: 1px;
  position: relative;
  width: 60%;
  height: 100%;
  border: 35px solid transparent;
  border-left-width: 42px;
  border-right-width: 42px;
  text-align: center;
  z-index: 1;
  display: table-cell;
  float: none;
  vertical-align: top;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 5px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 0px 5px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 5px 0 rgba(0, 0, 0, 0.1);
  -webkit-transform: translate(-30px, 30px);
  -moz-transform: translate(-30px, 30px);
  -ms-transform: translate(-30px, 30px);
  -o-transform: translate(-30px, 30px);
  transform: translate(-30px, 30px);
}
.element-v2 .info .h-lines {
  border-top-color: #8eaeba;
  border-bottom-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.element-v2 .info .v-lines {
  border-left-color: #8eaeba;
  border-right-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.element-v2 .info .content {
  width: 100%;
  height: 100%;
  padding: 30px;
  position: relative;
  z-index: 1;
}
.element-v2 .info .content p {
  margin-bottom: 0;
}
.element-v2 .info .sn-icons {
  float: none;
  position: relative;
  margin: 30px 0 10px;
  padding: 0;
  text-align: center;
  width: 100%;
}
.element-v2 .info .sn-icons a {
  color: #8eaeba;
}
.element-v2 .info h3 {
  margin-top: 12px;
  margin-bottom: 18px;
  position: relative;
  font-family: "Playfair Display", serif;
}
.element-v2 .info h3 small {
  display: block;
  font-size: 14pt;
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  text-transform: uppercase;
  font-style: italic;
}
.element-v2 .info p {
  width: 100%;
}
.element-v2::before {
  content: "";
  width: 337px;
  height: 230px;
  position: absolute;
  top: -48px;
  right: -112px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-large.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-transform: scaleX(-1) rotate(-16deg);
  -moz-transform: scaleX(-1) rotate(-16deg);
  -ms-transform: scaleX(-1) rotate(-16deg);
  -o-transform: scaleX(-1) rotate(-16deg);
  transform: scaleX(-1) rotate(-16deg);
}
.element-v2.photo-right {
  margin-bottom: 50px;
}
.element-v2.photo-right::before {
  top: auto;
  right: auto;
  left: -110px;
  bottom: -50px;
  -webkit-transform: scaleX(-1) rotate(165deg);
  -moz-transform: scaleX(-1) rotate(165deg);
  -ms-transform: scaleX(-1) rotate(165deg);
  -o-transform: scaleX(-1) rotate(165deg);
  transform: scaleX(-1) rotate(165deg);
}
.element-v2.photo-right .info {
  -webkit-transform: translate(30px, -30px);
  -moz-transform: translate(30px, -30px);
  -ms-transform: translate(30px, -30px);
  -o-transform: translate(30px, -30px);
  transform: translate(30px, -30px);
}

/* #Our Story
================================================== */
#our-story-title {
  font-size: 38pt;
  line-height: 38pt;
  text-align: center;
  color: #fff;
  font-family: "Playfair Display", serif;
}
#our-story-title .section-title {
  margin-bottom: 0;
}

.timeline {
  position: relative;
}
.timeline::before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -1px;
  background: #8eaeba;
  clear: both;
  -moz-transition: height 0.3s linear;
  -o-transition: height 0.3s linear;
  -webkit-transition: height 0.3s linear;
  transition: height 0.3s linear;
}
.timeline::after {
  content: "";
  display: table;
  clear: both;
}
.timeline .year {
  clear: both;
  padding: 7px 0;
  position: relative;
  z-index: 1;
  text-align: center;
  width: 100%;
  background-color: #fff;
  margin-bottom: 70px;
}
.timeline .year:first-child {
  padding-top: 0;
}
.timeline .year .neela-style {
  --offset: 7px;
  --border-size: 1px;
  font-family: "Playfair Display", serif;
  color: #8eaeba;
  font-size: 27pt;
  position: relative;
  padding: 4px 32px;
}
.timeline .year .neela-style .h-lines {
  border-top-color: #8eaeba;
  border-bottom-color: #8eaeba;
}
.timeline .year .neela-style .v-lines {
  border-left-color: #8eaeba;
  border-right-color: #8eaeba;
}
.timeline .date {
  background-color: #8eaeba;
  border: 10px solid #fff;
  padding: 26px 22px;
  display: inline-block;
  position: absolute;
  top: 50px;
  z-index: 10;
  right: 19%;
}
.timeline .date .neela-style {
  --offset: 7px;
  --border-size: 1px;
  font-family: "Playfair Display", serif;
  color: #fff;
  font-size: 18pt;
  position: relative;
  padding: 8px 18px;
}
.timeline .date .neela-style .h-lines {
  border-top-color: rgba(255, 255, 255, 0.3);
  border-bottom-color: rgba(255, 255, 255, 0.3);
}
.timeline .date .neela-style .v-lines {
  border-left-color: rgba(255, 255, 255, 0.3);
  border-right-color: rgba(255, 255, 255, 0.3);
}
.timeline .description-wrapper {
  position: relative;
  z-index: 10;
}
.timeline .description {
  background-color: #8eaeba;
  border: 10px solid #fff;
  padding: 30px;
  display: inline-block;
  position: relative;
  z-index: 10;
  text-align: center;
}
.timeline .description .neela-style {
  --offset: 9px;
  --border-size: 1px;
  color: #fff;
  position: relative;
  padding: 30px 20px;
}
.timeline .description .neela-style .h-lines {
  border-top-color: rgba(255, 255, 255, 0.3);
  border-bottom-color: rgba(255, 255, 255, 0.3);
}
.timeline .description .neela-style .v-lines {
  border-left-color: rgba(255, 255, 255, 0.3);
  border-right-color: rgba(255, 255, 255, 0.3);
}
.timeline .description h4 {
  float: none;
  text-align: center;
  margin: 0 auto 15px;
  position: relative;
  padding-bottom: 40px;
  color: #fff;
  display: inline-block;
  font-family: "Playfair Display", serif;
}
.timeline .description h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 19px;
  background-position: center center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='140' height='19' viewBox='0 0 140 19'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M64,2.022L71.87,9.5,64,16.976,56.132,9.5Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M70,2.022L77.882,9.5,70,16.976,62.119,9.5Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M77,2.022L84.869,9.5,77,16.976,69.13,9.5Z'/%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='M90.014,10.017V9H389v1.017H90.014Z'/%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='M-245,10.017V9H50.986v1.017H-245Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  -khtml-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
}
.timeline .description h4.has-icon {
  padding-left: 45px;
}
.timeline .description h4.has-icon i {
  position: absolute;
  left: -8px;
  top: -8px;
  font-size: 36pt;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.timeline .description p {
  margin: 0;
}
.timeline .template-1,
.timeline .template-2 {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  position: relative;
  margin-bottom: 100px;
}
.timeline .template-1::before, .timeline .template-1::after,
.timeline .template-2::before,
.timeline .template-2::after {
  content: "";
  width: 424px;
  height: 398px;
  position: absolute;
  top: -60px;
  right: -150px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-medium.svg);
  background-repeat: no-repeat;
}
.timeline .template-1::after,
.timeline .template-2::after {
  top: auto;
  right: auto;
  bottom: -100px;
  left: -150px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.timeline .template-1 .image-1,
.timeline .template-2 .image-1 {
  grid-column: 1/span 14;
  grid-row: 1;
  z-index: 1;
  max-width: 100%;
}
.timeline .template-1 .image-1 img,
.timeline .template-2 .image-1 img {
  border: 10px solid #fff;
  width: 100%;
  height: auto;
  display: block;
}
.timeline .template-1 .image-2,
.timeline .template-2 .image-2 {
  grid-column: 12/-1;
  grid-row: 1;
  padding-top: 25%;
  z-index: 1;
  max-width: 100%;
}
.timeline .template-1 .image-2 img,
.timeline .template-2 .image-2 img {
  border: 10px solid #fff;
  width: 100%;
  height: auto;
  display: block;
}
.timeline .template-1 .description-wrapper,
.timeline .template-2 .description-wrapper {
  grid-column: 3/span 12;
  grid-row: 1;
  margin-top: 100%;
}
.timeline .template-2::after {
  bottom: 20px;
}
.timeline .template-2 .date {
  top: 90px;
  right: 15%;
}
.timeline .template-2 .videoEmbed {
  grid-column: 1/span 16;
  grid-row: 1;
  z-index: 3;
  max-width: 100%;
}
.timeline .template-2 .videoEmbed img,
.timeline .template-2 .videoEmbed iframe,
.timeline .template-2 .videoEmbed embed,
.timeline .template-2 .videoEmbed object {
  border: 10px solid #fff;
  background: #fff;
  width: 100%;
  display: block;
}
.timeline .template-2 .image-1 {
  grid-column: 13/-1;
  padding-top: 35%;
  z-index: 1;
}
.timeline .template-2 .image-2 {
  grid-column: 2/span 12;
  padding-top: 65%;
  z-index: 2;
}
.timeline .template-2 .description-wrapper {
  grid-column: 11/span 12;
  grid-row: 1;
  margin-top: 150%;
}

.gallery-container {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  position: relative;
  margin-bottom: 100px;
}
.gallery-container::after {
  content: "";
  width: 424px;
  height: 398px;
  position: absolute;
  bottom: -75px;
  left: -165px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-medium.svg);
  background-repeat: no-repeat;
  -webkit-transform: scaleY(-1) rotate(-90deg);
  -moz-transform: scaleY(-1) rotate(-90deg);
  -ms-transform: scaleY(-1) rotate(-90deg);
  -o-transform: scaleY(-1) rotate(-90deg);
  transform: scaleY(-1) rotate(-90deg);
}
.gallery-container .description-wrapper {
  grid-column: 14/-1;
  grid-row: 1;
  margin-top: 10%;
  z-index: 1;
}
.gallery-container .description-wrapper .description {
  -moz-transition: transform 0.5s ease-in-out;
  -o-transition: transform 0.5s ease-in-out;
  -webkit-transition: transform 0.5s ease-in-out;
  transition: transform 0.5s ease-in-out;
}
.gallery-container .timeline-gallery-wrapper {
  grid-column: 1/span 18;
  grid-row: 1;
  position: relative;
  display: inline-block;
  max-width: 100%;
}
.gallery-container:hover .description {
  -webkit-transform: translateX(35%);
  -moz-transform: translateX(35%);
  -ms-transform: translateX(35%);
  -o-transform: translateX(35%);
  transform: translateX(35%);
}

.timeline_footer {
  clear: both;
  position: relative;
  z-index: 2;
  text-align: center;
  width: 100%;
  display: inline-block;
  line-height: 1;
}
.timeline_footer i {
  display: inline-block;
  font-size: 60pt;
  color: #8eaeba;
  margin-top: 21px;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.timeline_footer .punchline {
  font-size: 60pt;
  font-weight: 700;
  font-family: "Playfair Display", serif;
  color: #8eaeba;
  padding-bottom: 60px;
  text-transform: uppercase;
}
.timeline_footer .punchline small {
  display: block;
  font-size: 18pt;
  margin-bottom: 6px;
  font-weight: 400;
}

/* #Invite
================================================== */
.invite {
  color: #fff;
  position: relative;
  padding: 0;
  display: flex;
  margin-top: 25px;
  margin-bottom: 60px;
  text-align: center;
  --offset: 14px;
  --border-size: 2px;
}
.invite.neela-style .h-lines {
  border-top-color: #fff;
  border-bottom-color: #fff;
  z-index: 0;
}
.invite.neela-style .v-lines {
  border-left-color: #fff;
  border-right-color: #fff;
  z-index: 0;
}
.invite .invite_title {
  font-family: "Playfair Display", serif;
  font-size: 90pt;
  width: 48%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
  overflow: hidden;
}
.invite .invite_title .text {
  display: flex;
  flex-direction: column;
  line-height: 62px;
  text-align: center;
  position: relative;
  padding: 130px 80px;
}
.invite .invite_title .text::before, .invite .invite_title .text::after {
  content: "";
  width: 173px;
  height: 154px;
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='173' height='154' viewBox='0 0 173 154'%3E%3Cpath fill='%238eaeba' fill-rule='evenodd' d='M150.654,150.631c0.3-7.107,3.6-12.254.517-18.973-3.548-7.745-14.639-6.658-20.528-9.25-10.21-4.5-25.1-19.636-24.659-29.66,2.011,1.253,1.29,2.383,7.129,3.061,18.512,2.154,31.2,9.55,38,29.289-0.178-12.055-8.414-30.386-18.166-39.452-13.348,6.263-23.983,4.124-35.357,4.277,10.07-5.947,22.351-7.191,31.926-7.13-10.27-3.733-24.368-2.73-30.793-13.875,9.626,0.714,20.238,5.526,27.821,8.116l0-.006c-3.108-1.229-4.836-3.539-6.512-6.318a27.034,27.034,0,0,0-8.449-4.167c-8.354-1.857-15.43-1.311-19.861,4.171A4.015,4.015,0,0,1,90.6,73.408c0.55,1.645,2.577,3.918,3.166,6.147,0.28,1.06,1.879,1.1,1.857,2.86-0.042,3.132-3.129,3.329-3.807,1.978-0.971-1.942,1.012-3.09.9-4.36a10.358,10.358,0,0,0-2.507-5.769,62.958,62.958,0,0,1-3.363,9.9c-0.511,1.026-.019,3.005-0.693,4.105-0.917,1.5-3.3.691-3.755-.823-0.751-2.5,2.629-2.1,3.458-3.452,1.758-2.851,3.02-5.94,2.53-9.466a10.389,10.389,0,0,0-3.684,3.157c-0.564.64,0.008,1.972-.842,3-0.648.78-2.84,0.678-2.867-.743-0.046-2.282,2.07-1.939,3-2.849,1.542-1.512,3.6-2.9,4.3-4.021-4.247-.185-8.687.484-11.058,4.542-0.4.687,0.452,1.733-.428,3.628a1.364,1.364,0,0,1-2.393-.085c-0.9-1.367,1.6-2.7,2.237-3.814,2.6-4.529,7.544-4.911,12-5.108,1.134-1.639,1.958-2,2.409-1.894a24.8,24.8,0,0,1,7.026-5.112A92.55,92.55,0,0,0,75.468,70c-1.425,1.992-2.984,1.192-4.369,1.872a20.928,20.928,0,0,1-9.562,8.165c-1.431.633-1.738,2.508-3.144,1.8a1.435,1.435,0,0,1-.542-2.2c1.459-.981,2.043.316,3.178-0.091,3.361-1.206,6.106-3.677,8.664-6.854-1.339-.1-2.977.765-4.5,1.3-0.825.284-1.192,1.694-2.389,1.233a1.087,1.087,0,0,1-.272-2.041c1.086-.59,1.2.381,2.246,0.232A6.7,6.7,0,0,0,69.45,70.77c-4.939.4-9.165-.944-13.672-0.13a2.314,2.314,0,0,1-1.842,1.989,1.5,1.5,0,0,1-.974-2.461c0.9-1.1,1.527.016,3.129-.216,4-.584,9.461.66,13.295-0.13a9.7,9.7,0,0,0-5.72-3.171c-1.406-.306-1.322,1.51-2.661.317-0.917-.812-0.572-1.888.279-2.061,1.282-.257,1.678.784,2.527,1.061A22.772,22.772,0,0,1,70.183,69c-0.505-4.879-7.863-8.246-12.986-7.616-1.034.128-1.482,1.336-2.17,1.98-1.514,1.415-2.933-.858-2.224-1.774,1.1-1.422,1.79-.567,3.178-0.59,4.871-2.07,13.78,2.852,15.485,7.478,1.329,0.21,2.7-.664,3.687.488a98.437,98.437,0,0,1,24.4-4.989,80.531,80.531,0,0,0-8.573-3.941c-1.446.267-2.337-.446-3.2-0.782-2.683,1.121-4.685,3.479-7.8,4.134-0.621.133-1.117,1.319-2.4,1.2a1.587,1.587,0,0,1-.844-2.572c1.3-1.215,2.139-.017,3.157.652A19.408,19.408,0,0,0,87.246,58.3a5.093,5.093,0,0,0-1.464-1.539c-2.031-1.441-5.108-1.886-7.359-1.961-0.919-.031-2.371,1-3.47.888a1.353,1.353,0,0,1-.42-2.483c1.259-.957,2.013.688,3.8,0.926A29.363,29.363,0,0,1,87.3,56.392c-1.065-2.289-4.654-5.751-7.723-7.192-0.936-.437-1.746.95-3.548-0.436l0,0a9.894,9.894,0,0,1-.926.488c-7.038,3.163-21.7,3.807-26.634-1.057,1.575-.452,1.819.44,4.874-2.244,9.687-8.509,19.361-11.6,32.631-5.968-6.261-5.485-19.474-9.734-28.642-8.92-2.98,9.75-9.006,14.222-14.2,20.133,1.621-7.921,6.678-14.8,11.149-19.69C47.6,35.049,41.574,42.752,32.883,40.9c4.837-4.62,12.241-7.846,17.084-10.545-4.012,2.012-7.691-.525-13.256-0.607C32.6,29.684,27.837,33.4,22.8,33.16c-10.2-.487-17.25-6.753-21.769-14.221,11.584,1.867,19.724-3.341,29.358,1.555,4.862,2.47,7.126,10.863,15.384,8.313-6.265-2.4-9.455-6.156-13.4-9.462-2.344-1.964-5.2-3.362-6.776-6.01,5.2,0.11,9.6,3.471,12.674,5.909-1.932-3.517-5.541-6.064-5.02-11C38,11.962,43.627,17.772,46.065,23.009c0.931,2,.978,4.909,3.043,5.853,4.144,1.893,7.732-1.292,14.04.816-4.216-6.068-13.008-6.52-16.787-12.468-2.972-4.679-3.6-9.687-4.647-15.519C51.735,7.3,66.555,12.97,66.469,25.108c-0.014,1.946-1.446,3.33-.372,4.518a60.644,60.644,0,0,1,8.426,1.414A40.859,40.859,0,0,1,67.1,5.543c3.3,3.5,5.605,8.117,7.547,12.516,0.125-2.447-.553-5.461.521-7.419,4.188,5.3,7.1,11.972,6.847,19.045A19.976,19.976,0,0,1,80.86,34,65.916,65.916,0,0,1,92,41.857c-3.914-9.3-3.311-19.347-.559-28.505,9.215,14.266,22.685,17.962,26.761,33.072,2.057,7.626-4.755,18.207,5.978,24.609-3.494-9.064-2.551-16.141-2.823-23.591-0.162-4.427-1.431-8.859-.112-13.12,4.921,5.7,5.572,13.691,5.933,19.362,1.9-5.492,1.141-11.844,6.942-16.075,0.612,8.717-.173,20.409-3.432,28.113-1.243,2.941-4.321,5.816-3.334,8.954,1.982,6.293,8.874,7.058,12.721,15.886,2.428-10.423-5.6-20.315-2.878-30.152,2.143-7.738,6.913-13.269,12.152-20.062C153.04,56.564,161.3,78.006,148.194,89.7c-2.1,1.875-4.975,1.679-5.21,3.987a87.634,87.634,0,0,1,6.642,10.432c2.736-12.2,9.074-23.236,20.174-32.734-0.556,6.945-3.282,13.907-6.122,20.264,2.747-2.24,5.324-5.9,8.465-6.641-1.632,9.648-5.971,19.255-13.806,25.851-1.646,1.385-3.829,1.961-5.745,2.942a93.846,93.846,0,0,1,1.835,30.687c-0.237,1.942-.347,4.347-0.691,6.3C153.2,152,150.5,151.981,150.654,150.631ZM102.827,88.56c7.678,0.3,17.93,1.206,28.549-3.425C123.005,82.734,113.238,84.656,102.827,88.56ZM35.757,39.565c5.507,0.751,10.921-3.631,14.964-7.333C45.476,31.938,40.61,37.268,35.757,39.565ZM8.013,24.167c4.562,7.216,20.036,8.694,25.1,3.355C24.168,25.4,17.649,27.486,8.013,24.167Zm24.843,1.187C29.125,20.117,13.7,18.954,7.47,21.5,16.151,25.842,25.886,23.983,32.856,25.353Zm1.433-14.731c2.076,5.971,7.548,9.977,11.04,15.129C44.569,20.341,39.591,14.505,34.289,10.622ZM29.3,15.165C34.1,19.228,37.678,24,44.624,26.821,40.327,22.466,36.389,17.9,29.3,15.165ZM44.851,47.876c3.714-3.8,8.931-8.649,11.482-16.243C51.222,34.821,47.677,40.725,44.851,47.876ZM61.746,25.524c-1.174-7.488-12.773-11.811-17.77-18.189C45.787,15.777,51.767,21.807,61.746,25.524ZM45.674,5.69c1.264,3.623,6.947,6.389,10.841,9.618C60.427,18.55,62.57,22.9,64.273,27.04,67.1,16.025,54.781,10.906,45.674,5.69ZM68.321,8.808c-0.82,8.531,3.188,17.136,8.446,23.254C76.862,23.95,72.278,15.679,68.321,8.808ZM75.8,13.749c-1.173,6.639,1.565,13.88,3.492,19.828C83.538,26.476,77.472,19.634,75.8,13.749Zm56.771,27.289c-4.4,8.028-3.4,17.8-5.543,26.558C132.1,61.526,133.542,50.509,132.574,41.038Zm-9.709-.958c0.291,9.11-1.363,17.588,2.334,27.8C125.713,59.028,126.8,50.363,122.865,40.08ZM81.394,42.956c-7.937-.947-14.951,4.679-22.608,6.128C65.828,50.762,75.8,47.614,81.394,42.956ZM52.81,47.906c9.654,0.288,18.735-5.387,28.04-7.616C68.4,35.314,60.372,42.53,52.81,47.906ZM94.927,22.768C98.677,36.313,110.1,44.975,115.379,53.8,117.385,44.7,103.7,26.994,94.927,22.768ZM113.3,56.18C106.914,44.5,98.365,39.52,92.594,25.938,89.269,37.846,102.667,55.918,113.3,56.18Zm1.1,6.025c-3.914-4.473-12.517-5.992-17.141-11.644A36.358,36.358,0,0,1,94.453,46.6a10.38,10.38,0,0,0-5.1-3.617c-4.145-1.3-7.727,1.736-10.952,4.15a8.956,8.956,0,0,0,1.667,1.55c3.546,1.98,6.8,4.905,8.31,7.828a5.919,5.919,0,0,0,.311-4.819c-0.606-1.162-1.931-1.185-1.187-2.724,0.348-.719,1.664-0.509,1.991.008,0.538,0.851.06,1.649,0.022,2.732-0.067,1.964.339,3.167-.431,5.693A5.061,5.061,0,0,1,91.5,59.086c4.366,2.29,5.956,3.357,9.41,4.472,2.854,0.921,8.05,1.064,12.3,2.491a29.45,29.45,0,0,1,5.833,2.985A41.484,41.484,0,0,0,114.4,62.205Zm-11.464,8.508c4.528,6.649,14.472,8.216,22.36,8.969C120.534,73.758,110.1,73.7,102.938,70.714Zm40.236,14.31c6.9-8.531.3-25.2,2.309-36.763C138.178,58.4,137.5,70.687,143.174,85.023Zm5.718-36.533c-2.663,4.877-.129,13.672.178,20.993,0.309,7.353-2.282,13.878-5.076,19.73C158.552,81.558,152.115,63.344,148.892,48.49Zm18.586,27.374c-9.949,7.4-15.3,20.064-16.773,31.656C159.5,99.748,163.936,86.789,167.478,75.864Zm1.944,12.842c-8.261,5.184-13.379,15.157-17.9,23C163.255,109.377,164.722,96.213,169.422,88.706Zm-58.92,8.44c9.039,10.66,23.923,14.912,35.326,22.753C139.108,101.678,123.594,100.057,110.5,97.146ZM143.5,123.071c-6.67-9.453-19.5-11.531-28.47-18.357C120.044,113.914,133.081,121.579,143.5,123.071Z'/%3E%3C/svg%3E");
  background-size: contain;
  -khtml-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.invite .invite_title .text::after {
  top: auto;
  right: auto;
  bottom: 11px;
  left: 4px;
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  -o-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
.invite .invite_title .text small {
  display: block;
  font-size: 16pt;
  top: 10px;
  left: 50%;
  position: relative;
  text-transform: uppercase;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
.invite .invite_title .text span:last-child {
  margin-left: 24px;
}
.invite .invite_info {
  width: 52%;
  font-size: 12pt;
  font-weight: 300;
  display: flex;
  flex-direction: column;
  padding: 40px 30px;
  background-color: rgba(142, 174, 186, 0.7);
  color: #fff;
}
.invite .invite_info h2 {
  margin-bottom: 8px;
  color: #fff;
  font-family: "Playfair Display", serif;
}
.invite .invite_info h2 small {
  font-size: 16pt;
}
.invite .invite_info .date {
  font-family: "Playfair Display", serif;
  font-size: 26pt;
  font-weight: 700;
  padding: 35px 0 41px;
  margin: 20px 0;
  position: relative;
}
.invite .invite_info .date::before, .invite .invite_info .date::after {
  content: "";
  width: 100%;
  height: 19px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='128' height='19' viewBox='0 0 128 19'%3E%3Cpath fill='%23fff' d='M3.74,13.725l0.038,0.17a0.724,0.724,0,0,1-.244.489,0.834,0.834,0,0,1-.553.248L2.86,14.618a1.013,1.013,0,0,1-.609-0.467A1.659,1.659,0,0,1,2,13.272l0.016-.22a3.255,3.255,0,0,1,1.256-1.906c1.8-1.5,4.283-2,6.732-2.162q1.109-.072,2.215-0.071c5.872,0,11.731,1.442,17.614,2.9S41.617,14.743,47.6,14.744a32.472,32.472,0,0,0,4.79-.343,13.813,13.813,0,0,0,5.618-2.078,6.491,6.491,0,0,0,3.082-4.841C61.1,7.374,61.1,7.266,61.1,7.156a4.016,4.016,0,0,0-.454-1.882A2.426,2.426,0,0,0,59.16,4.029a2.2,2.2,0,0,0-.575-0.077,2.837,2.837,0,0,0-1.63.578,5.389,5.389,0,0,0-1.2,1.214h0a8.8,8.8,0,0,0-1.643,5.061,6.377,6.377,0,0,0,1.525,4.3,7.659,7.659,0,0,0,5.253,2.3q0.359,0.024.718,0.024a12.169,12.169,0,0,0,8.339-3.561,12.482,12.482,0,0,0,3.89-8.353l0-.229a6.02,6.02,0,0,0-.718-2.873,3.392,3.392,0,0,0-.935-1.088A2.317,2.317,0,0,0,70.82.871l-0.246.013a2.791,2.791,0,0,0-2,1.316,7.094,7.094,0,0,0-.936,2.208,17.158,17.158,0,0,0-.6,4.233,10.349,10.349,0,0,0,.378,2.852,7.818,7.818,0,0,0,1.8,3.194,5.814,5.814,0,0,0,3.131,1.785,5.4,5.4,0,0,0,.994.092,6.954,6.954,0,0,0,4.187-1.6,14.982,14.982,0,0,0,3.15-3.443A6.4,6.4,0,0,0,82.016,8.2l0-.093a3.235,3.235,0,0,0-.593-1.854,2.282,2.282,0,0,0-1.655-1L79.6,5.248a2.406,2.406,0,0,0-1.433.511,3.528,3.528,0,0,0-.97,1.134,4.507,4.507,0,0,0-.546,2.176,6.08,6.08,0,0,0,1.094,3.347,6.851,6.851,0,0,0,2.679,2.348,10.394,10.394,0,0,0,4.531.909,28.454,28.454,0,0,0,4.151-.382c10.132-1.5,20.19-3.819,30.3-3.817q1.119,0,2.24.039a9.778,9.778,0,0,1,2.139.259,3.273,3.273,0,0,1,1.647.928A2.282,2.282,0,0,1,126,14.254a2.915,2.915,0,0,1-.608,1.776,1.878,1.878,0,0,1-1.476.769l-0.223-.014-0.059.509,0.1-.5a2.048,2.048,0,0,1-1.045-.5,0.781,0.781,0,0,1-.251-0.553,0.741,0.741,0,0,1,.11-0.379,0.519,0.519,0,0,0-.163-0.7,0.493,0.493,0,0,0-.687.166,1.781,1.781,0,0,0-.258.917,1.814,1.814,0,0,0,.546,1.28,3.018,3.018,0,0,0,1.541.775l0.044,0.007a2.765,2.765,0,0,0,.341.021,2.867,2.867,0,0,0,2.25-1.146A3.939,3.939,0,0,0,127,14.254a3.321,3.321,0,0,0-.829-2.248,4.247,4.247,0,0,0-2.13-1.224,10.665,10.665,0,0,0-2.355-.292q-1.138-.041-2.275-0.04c-10.262,0-20.364,2.338-30.446,3.827a27.728,27.728,0,0,1-4.008.371,9.442,9.442,0,0,1-4.111-.814,5.853,5.853,0,0,1-2.278-2,5.039,5.039,0,0,1-.917-2.766,3.46,3.46,0,0,1,.418-1.675,2.541,2.541,0,0,1,.687-0.808A1.424,1.424,0,0,1,79.6,6.272l0.09,0a1.291,1.291,0,0,1,.929.586,2.206,2.206,0,0,1,.4,1.248V8.17a5.463,5.463,0,0,1-1.167,2.769,13.98,13.98,0,0,1-2.928,3.208,5.977,5.977,0,0,1-3.589,1.395,4.427,4.427,0,0,1-.814-0.076,4.827,4.827,0,0,1-2.593-1.489,6.807,6.807,0,0,1-1.556-2.769,9.317,9.317,0,0,1-.339-2.566A16.125,16.125,0,0,1,68.6,4.668a6.1,6.1,0,0,1,.788-1.879A1.8,1.8,0,0,1,70.672,1.9l0.148-.008a1.324,1.324,0,0,1,.8.267,2.833,2.833,0,0,1,.9,1.3,5.23,5.23,0,0,1,.335,1.827l0,0.187a11.452,11.452,0,0,1-3.578,7.651,11.185,11.185,0,0,1-7.653,3.281c-0.216,0-.433-0.007-0.65-0.023a6.69,6.69,0,0,1-4.583-1.967,5.342,5.342,0,0,1-1.264-3.609,7.776,7.776,0,0,1,1.446-4.452,4.33,4.33,0,0,1,.963-0.983,1.868,1.868,0,0,1,1.06-.4,1.2,1.2,0,0,1,.321.043,1.425,1.425,0,0,1,.868.752A2.975,2.975,0,0,1,60.1,7.156l-0.01.244a5.467,5.467,0,0,1-2.638,4.072,12.9,12.9,0,0,1-5.21,1.917,31.521,31.521,0,0,1-4.646.332c-5.818,0-11.65-1.442-17.533-2.9S18.249,7.892,12.217,7.891q-1.137,0-2.279.073a12.477,12.477,0,0,0-7.3,2.388A4.24,4.24,0,0,0,1.026,12.9,2.465,2.465,0,0,0,1,13.272a2.7,2.7,0,0,0,.418,1.443,2.016,2.016,0,0,0,1.219.9,1.54,1.54,0,0,0,.345.039,1.807,1.807,0,0,0,1.236-.525,1.738,1.738,0,0,0,.559-1.235,1.417,1.417,0,0,0-.14-0.62,0.494,0.494,0,0,0-.668-0.234A0.517,0.517,0,0,0,3.74,13.725Z'/%3E%3C/svg%3E");
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.invite .invite_info .date::after {
  top: auto;
  bottom: 0;
  -webkit-transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -ms-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
}
.invite .invite_info .date small {
  display: block;
  font-size: 18pt;
  font-weight: 400;
}
.invite .invite_info h5 {
  color: #fff;
  margin-top: 12px;
  font-family: "Playfair Display", serif;
}

.overflow-content {
  z-index: 1;
  /*margin: 100px 0;*/
  position: relative;
  padding: 70px 0;
}

.overflow-content-over {
  z-index: 1;
}

.overflow-image-wrapper {
  position: relative;
}

.overflow-image-text {
  width: 50%;
}

.overflow-image {
  position: absolute;
  right: 0;
  top: -140px;
  bottom: -140px;
  width: 38%;
  height: auto;
  border: 20px solid #fff;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.overflow-image img {
  border: 20px solid #fff;
  display: none;
}
.overflow-image.flower::before {
  content: "";
  width: 424px;
  height: 398px;
  position: absolute;
  top: 50%;
  right: -274px;
  margin-top: -212px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-medium.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.menu-section {
  position: relative;
  background-color: #8eaeba;
}
.menu-section::before {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  width: 33.3%;
  height: 100%;
  background-color: #8eaeba;
  z-index: 0;
}

.menu-wrapper {
  padding: 60px 56px;
  background-color: #8eaeba;
  color: #fff;
  z-index: 1;
}
.menu-wrapper .neela-style {
  --offset: 10px;
  --border-size: 1px;
  color: #fff;
  position: relative;
  padding: 46px 46px;
  z-index: 0;
}
.menu-wrapper .neela-style .h-lines {
  border-top-color: rgba(255, 255, 255, 0.5);
  border-bottom-color: rgba(255, 255, 255, 0.5);
}
.menu-wrapper .neela-style .v-lines {
  border-left-color: rgba(255, 255, 255, 0.5);
  border-right-color: rgba(255, 255, 255, 0.5);
}
.menu-wrapper .menu-top-flowers::before, .menu-wrapper .menu-top-flowers::after {
  content: "";
  width: 170px;
  height: 151px;
  position: absolute;
  top: 8px;
  right: 10px;
  z-index: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url(../images/flower-small.svg);
  background-size: contain;
  -khtml-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
}
.menu-wrapper .menu-top-flowers::after {
  right: auto;
  left: 10px;
  -webkit-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
}
.menu-wrapper .menu-bottom-flowers::before, .menu-wrapper .menu-bottom-flowers::after {
  content: "";
  width: 170px;
  height: 151px;
  position: absolute;
  bottom: 8px;
  right: 10px;
  z-index: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url(../images/flower-small.svg);
  background-size: contain;
  -khtml-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
  -webkit-transform: scaleX(-1) rotate(180deg);
  -moz-transform: scaleX(-1) rotate(180deg);
  -ms-transform: scaleX(-1) rotate(180deg);
  -o-transform: scaleX(-1) rotate(180deg);
  transform: scaleX(-1) rotate(180deg);
}
.menu-wrapper .menu-bottom-flowers::after {
  right: auto;
  left: 10px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.menu-wrapper h2.section-title-lg {
  color: #fff;
}
.menu-wrapper .menu-items {
  list-style: none;
  margin: 0;
  padding: 0;
}
.menu-wrapper .menu-items li {
  position: relative;
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 3px;
}
.menu-wrapper .menu-items li h3 {
  color: #fff;
  text-transform: uppercase;
  font-size: 19pt;
  margin-bottom: 10px;
}
.menu-wrapper .menu-items li h4 {
  color: #fff;
  text-transform: uppercase;
  font-size: 12pt;
  margin-top: 12px;
  margin-bottom: 4px;
}
.menu-wrapper .menu-items li h4 ~ h4 {
  margin-top: 25px;
}
.menu-wrapper .menu-items li p {
  font-style: italic;
}
.menu-wrapper .menu-items li:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -25px;
  width: 50px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.5);
}
.menu-wrapper .menu-items li:last-child:after {
  display: none;
}

.half-img {
  padding: 0;
  margin: 0;
  min-height: 0;
  background-clip: border-box;
  background-origin: padding-box;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 50%;
}

/* #Wedding Location
================================================== */
.map-info-container {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  position: relative;
  margin-bottom: 50px;
}
.map-info-container::before {
  content: "";
  width: 1px;
  height: calc(100% + 59px);
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -1px;
  background: #8eaeba;
  clear: both;
}
.map-info-container .info-wrapper {
  grid-column: 1/span 9;
  grid-row: 1;
  margin-top: 10%;
  z-index: 1;
}
.map-info-container .info-wrapper .location-info {
  background-color: #8eaeba;
  border: 10px solid #fff;
  padding: 30px;
  display: inline-block;
  position: relative;
  z-index: 10;
  text-align: center;
  width: 100%;
  -moz-transition: transform 0.5s ease-in-out;
  -o-transition: transform 0.5s ease-in-out;
  -webkit-transition: transform 0.5s ease-in-out;
  transition: transform 0.5s ease-in-out;
}
.map-info-container .info-wrapper .location-info .neela-style {
  --offset: 9px;
  --border-size: 1px;
  color: #fff;
  position: relative;
  padding: 24px 12px;
}
.map-info-container .info-wrapper .location-info .neela-style .h-lines {
  border-top-color: rgba(255, 255, 255, 0.5);
  border-bottom-color: rgba(255, 255, 255, 0.5);
}
.map-info-container .info-wrapper .location-info .neela-style .v-lines {
  border-left-color: rgba(255, 255, 255, 0.5);
  border-right-color: rgba(255, 255, 255, 0.5);
}
.map-info-container .info-wrapper .location-info h4 {
  float: none;
  text-align: left;
  margin: 0 auto 25px;
  position: relative;
  color: #fff;
  display: inline-block;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  font-size: 25pt;
}
.map-info-container .info-wrapper .location-info h4.has-icon {
  padding-left: 80px;
  padding-top: 10px;
}
.map-info-container .info-wrapper .location-info h4.has-icon i {
  position: absolute;
  left: 0px;
  top: 6px;
  font-size: 50pt;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.map-info-container .info-wrapper .location-info h4 small {
  display: block;
  font-weight: 400;
  font-size: 16pt;
}
.map-info-container .info-wrapper .location-info h5 {
  color: #fff;
  font-family: "Poppins", sans-serif;
  font-size: 15pt;
}
.map-info-container .info-wrapper .location-info .info-map-divider {
  position: relative;
  width: 100%;
  height: 19px;
  margin: 30px 0;
}
.map-info-container .info-wrapper .location-info .info-map-divider::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 19px;
  background-position: center center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='140' height='19' viewBox='0 0 140 19'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M64,2.022L71.87,9.5,64,16.976,56.132,9.5Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M70,2.022L77.882,9.5,70,16.976,62.119,9.5Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M77,2.022L84.869,9.5,77,16.976,69.13,9.5Z'/%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='M90.014,10.017V9H389v1.017H90.014Z'/%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='M-245,10.017V9H50.986v1.017H-245Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.map-info-container .info-wrapper .location-info p {
  margin: 0;
  font-style: italic;
  font-weight: 300;
}
.map-info-container .map-wrapper {
  grid-column: 6/-1;
  grid-row: 1;
  position: relative;
  display: inline-block;
  max-width: 100%;
}
.map-info-container .location-info.open {
  -webkit-transform: translateX(-25%);
  -moz-transform: translateX(-25%);
  -ms-transform: translateX(-25%);
  -o-transform: translateX(-25%);
  transform: translateX(-25%);
}

#map_canvas {
  width: 100%;
  height: 520px;
  position: relative;
  border: 15px solid #fff;
  background: #fff;
}
#map_canvas img {
  max-width: none;
}

.marker {
  width: 40px;
  height: 40px;
  cursor: pointer;
  text-align: center;
  background-color: #fff;
  color: #8eaeba;
  border: 2px solid #8eaeba;
  font-size: 16pt;
  line-height: 35px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.marker::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 11px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 9px 0 9px;
  border-color: #ffffff transparent transparent transparent;
  line-height: 0px;
  _border-color: #ffffff #000000 #000000 #000000;
  _filter: progid:DXImageTransform.Microsoft.Chroma(color="#000000");
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.marker::before {
  content: "";
  position: absolute;
  bottom: -11px;
  left: 10px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 10px 0 10px;
  border-color: #8eaeba transparent transparent transparent;
  line-height: 0px;
  _border-color: #8eaeba #000000 #000000 #000000;
  _filter: progid:DXImageTransform.Microsoft.Chroma(color="#000000");
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.marker:hover {
  background-color: #8eaeba;
  border-color: #fff;
  color: #fff;
}
.marker:hover::after {
  border-color: #8eaeba transparent transparent transparent;
}
.marker:hover::before {
  border-color: #ffffff transparent transparent transparent;
}

.marker > div {
  margin: 0;
  padding: 0;
}

.infoWindow {
  width: 200px;
}

.map_pins {
  padding: 4px 0;
  text-align: center;
  background-color: #fff;
  position: absolute;
  right: 0;
  top: 475px;
}
.map_pins .pins {
  list-style: none;
  color: #adb1b5;
  padding: 0;
  margin: 0;
  font-size: 14px;
}
.map_pins .pins li {
  display: inline-block;
  padding: 0;
  margin: 6px 14px;
}
.map_pins .pins li i,
.map_pins .pins li .fa {
  margin-right: 5px;
  font-size: 14pt;
  position: relative;
  top: 1px;
}

.wedding-details {
  text-align: center;
  position: relative;
  margin-bottom: 30px;
  padding: 0 30px 30px;
}
.wedding-details::after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 20%;
  height: 60%;
  width: 1px;
  background: #e3e3e3;
}
.wedding-details > i {
  color: #8eaeba;
  font-size: 60pt;
  line-height: 0;
}
.wedding-details h4 {
  font-family: "Playfair Display", serif;
  font-size: 35pt;
  margin-bottom: 38px;
}
.wedding-details h4 small {
  font-family: "Poppins", sans-serif;
  font-size: 11pt;
  display: block;
  margin-top: 6px;
}
.wedding-details:last-child::after {
  display: none;
}
.wedding-details.light {
  color: #fff;
}
.wedding-details.light i,
.wedding-details.light h4 {
  color: #fff;
}

#map_canvas_full {
  width: 100%;
  height: 520px;
  position: relative;
}
#map_canvas_full img {
  max-width: none;
}

.map_pins_full {
  padding: 11px 0;
  text-align: center;
  background-color: #8eaeba;
  color: #fff;
}
.map_pins_full .pins {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
}
.map_pins_full .pins li {
  display: inline-block;
  padding: 0;
  margin: 6px 18px;
}
.map_pins_full .pins li i,
.map_pins_full .pins li .fa {
  margin-right: 9px;
  font-size: 14pt;
  position: relative;
  top: 1px;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}

/* #Bridesmaids & Groomsmen
================================================== */
.bmaid-gmen .image,
.bmaid-gmen-color .image {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.bmaid-gmen .image .hover-info,
.bmaid-gmen-color .image .hover-info {
  border: 35px solid transparent;
}
.bmaid-gmen .image .hover-info .content,
.bmaid-gmen-color .image .hover-info .content {
  height: auto;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
.bmaid-gmen .image .hover-info h3 small,
.bmaid-gmen-color .image .hover-info h3 small {
  font-size: 12pt;
  margin-top: 8px;
}

.bmaid-gmen-color .image .hover-info {
  background-color: #8eaeba;
}
.bmaid-gmen-color .image .hover-info .h-lines {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
.bmaid-gmen-color .image .hover-info .v-lines {
  border-left-color: #fff;
  border-right-color: #fff;
}
.bmaid-gmen-color .image .hover-info h3,
.bmaid-gmen-color .image .hover-info .sn-icons a {
  color: #fff;
}

/* #Testimonials
================================================== */
.side-flowers-light {
  overflow: hidden;
}
.side-flowers-light::after, .side-flowers-light::before {
  content: "";
  width: 407px;
  height: 804px;
  position: absolute;
  top: 50%;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-large-light.svg);
  background-repeat: no-repeat;
  -khtml-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
}
.side-flowers-light::before {
  left: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
.side-flowers-light::after {
  right: 0;
  -webkit-transform: scaleX(-1) translateY(-50%);
  -moz-transform: scaleX(-1) translateY(-50%);
  -ms-transform: scaleX(-1) translateY(-50%);
  -o-transform: scaleX(-1) translateY(-50%);
  transform: scaleX(-1) translateY(-50%);
}

.side-flowers {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  z-index: -1;
  top: 0;
  left: 0;
}
.side-flowers::after, .side-flowers::before {
  content: "";
  width: 782px;
  height: 534px;
  position: absolute;
  top: 50%;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-large.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.side-flowers::before {
  left: -354px;
  -webkit-transform: scaleX(-1) translateY(-50%) rotate(-60deg);
  -moz-transform: scaleX(-1) translateY(-50%) rotate(-60deg);
  -ms-transform: scaleX(-1) translateY(-50%) rotate(-60deg);
  -o-transform: scaleX(-1) translateY(-50%) rotate(-60deg);
  transform: scaleX(-1) translateY(-50%) rotate(-60deg);
}
.side-flowers::after {
  right: -354px;
  -webkit-transform: scaleX(-1) translateY(-50%) rotate(104deg);
  -moz-transform: scaleX(-1) translateY(-50%) rotate(104deg);
  -ms-transform: scaleX(-1) translateY(-50%) rotate(104deg);
  -o-transform: scaleX(-1) translateY(-50%) rotate(104deg);
  transform: scaleX(-1) translateY(-50%) rotate(104deg);
}

section.side-flowers {
  position: relative;
  z-index: 0;
}

.testimonials .owl-item .item {
  border: 0;
  text-align: center;
}
.testimonials .owl-item .item blockquote {
  padding: 20px 75px;
  position: relative;
}
.testimonials .owl-item .item blockquote::before, .testimonials .owl-item .item blockquote::after {
  content: "“";
  position: absolute;
  font-family: "Playfair Display", serif;
  font-size: 145pt;
  line-height: 1;
  left: 0;
  top: 50%;
  margin-top: -68px;
  -khtml-opacity: 0.26;
  -moz-opacity: 0.26;
  opacity: 0.26;
}
.testimonials .owl-item .item blockquote::after {
  content: "”";
  left: auto;
  right: 0;
}
.testimonials .owl-item .item .author {
  margin-top: 45px;
  margin-bottom: 20px;
}
.testimonials .owl-item .item .author h3 {
  font-family: "Playfair Display", serif;
  color: #73777b;
}
.testimonials .owl-item .item .author h3 small {
  position: relative;
  display: block;
  font-family: "Poppins", sans-serif;
  font-size: 11pt;
  font-style: italic;
  margin-top: 32px;
}
.testimonials .owl-item .item .author h3 small::before {
  content: "";
  position: absolute;
  top: -16px;
  left: 50%;
  margin-left: -30px;
  width: 60px;
  height: 1px;
  background-color: #73777b;
  -khtml-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
}

.light .owl-item .item {
  color: #fff;
}
.light .owl-item .item .author h3 {
  color: #fff;
}
.light .owl-item .item .author h3 small::before {
  background-color: #fff;
}

/* #Gift Registry
================================================== */
#giftregistry {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.wedding-gifts {
  list-style: none;
  margin-bottom: 35px;
  margin-top: 15px;
  padding: 0;
}
.wedding-gifts li {
  position: relative;
  display: block;
  color: #8eaeba;
  background-color: #fff;
  padding: 35px;
  margin-bottom: 35px;
}
.wedding-gifts li .neela-style {
  --offset: 7px;
  --border-size: 1px;
  color: #8eaeba;
  position: relative;
  padding: 32px;
  z-index: 0;
}
.wedding-gifts li .neela-style .h-lines {
  border-top-color: rgba(142, 174, 186, 0.5);
  border-bottom-color: rgba(142, 174, 186, 0.5);
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.wedding-gifts li .neela-style .v-lines {
  border-left-color: rgba(142, 174, 186, 0.5);
  border-right-color: rgba(142, 174, 186, 0.5);
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.wedding-gifts li > div > i {
  position: relative;
  display: inline-block;
  line-height: 1;
  font-size: 50pt;
  z-index: 2;
  top: -3px;
  left: 0;
  width: 20%;
  text-align: right;
  margin-right: 7%;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.wedding-gifts li h3 {
  position: relative;
  display: inline-block;
  line-height: 1;
  z-index: 2;
  right: 0;
  width: 71%;
  font-size: 26pt;
  font-family: "Playfair Display", serif;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.wedding-gifts li .info {
  position: relative;
  z-index: 2;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  top: -40px;
  text-align: center;
  max-height: 0;
}
.wedding-gifts li .info img {
  background: rgba(255, 255, 255, 0.15);
  margin: 15px auto;
  padding: 5px 15px;
  display: inline-block;
  width: 40%;
}
.wedding-gifts li .info .btn {
  margin: 20px 10px;
}
.wedding-gifts li:hover .neela-style {
  height: auto;
}
.wedding-gifts li:hover .neela-style .h-lines {
  border-top-color: #8eaeba;
  border-bottom-color: #8eaeba;
}
.wedding-gifts li:hover .neela-style .v-lines {
  border-left-color: #8eaeba;
  border-right-color: #8eaeba;
}
.wedding-gifts li:hover .info {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  top: 20px;
  float: none;
  max-height: 500px;
}

.progress-wrapper {
  margin-bottom: 32px;
  color: #8eaeba;
  font-family: "Playfair Display", serif;
  font-size: 15pt;
}
.progress-wrapper label {
  display: block;
  width: 100%;
  margin-bottom: 2px;
}
.progress-wrapper label small {
  float: right;
}
.progress-wrapper .progress {
  height: 7px;
  background: 0;
  border-bottom: 1px solid #8eaeba;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.progress-wrapper .progress .progress-bar {
  width: 0;
  background-color: #8eaeba;
  -moz-transition: width 0.5s ease-in-out;
  -o-transition: width 0.5s ease-in-out;
  -webkit-transition: width 0.5s ease-in-out;
  transition: width 0.5s ease-in-out;
}

.partners {
  text-align: center;
}
.partners .item {
  display: inline-block;
  margin: 12px;
  vertical-align: middle;
  padding: 20px 15px;
}
.partners .item img {
  max-width: 100%;
}

/* #Gallery
================================================== */
.gallery-wrapper {
  position: relative;
}
.gallery-wrapper .gallery-left,
.gallery-wrapper .gallery-right {
  position: absolute;
  top: 50%;
  left: 15px;
  margin-top: -40px;
  color: #fff;
  z-index: 2;
  cursor: pointer;
  padding: 10px;
  display: inline-block;
  font-size: 36pt;
  line-height: 1;
  border: 5px solid transparent;
  -webkit-transform: translate(0, 0);
  -moz-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.gallery-wrapper .gallery-left:hover,
.gallery-wrapper .gallery-right:hover {
  -khtml-opacity: 1 !important;
  -moz-opacity: 1 !important;
  opacity: 1 !important;
}
.gallery-wrapper .gallery-right {
  right: 15px;
  left: auto;
}
.gallery-wrapper:hover .gallery-left,
.gallery-wrapper:hover .gallery-right {
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}

.gallery-scroller {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  margin-top: 30px;
  position: relative;
  bottom: -5px;
}
.gallery-scroller ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.gallery-scroller li {
  display: inline-block;
  position: relative;
  width: 380px;
  height: 380px;
  overflow: hidden;
  padding: 0;
  margin: -5px -3px 0 0;
  background-color: #8eaeba;
}
.gallery-scroller li img {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.gallery-scroller li:hover img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
.gallery-scroller li .hover-info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgba(86, 125, 140, 0.7);
  z-index: 1;
  text-align: center;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.gallery-scroller li .hover-info > a {
  position: relative;
  top: 25%;
  -webkit-transform: translateY(-87%);
  -moz-transform: translateY(-87%);
  -ms-transform: translateY(-87%);
  -o-transform: translateY(-87%);
  transform: translateY(-87%);
}
.gallery-scroller li:hover .hover-info {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.gallery-scroller li:hover .hover-info > a {
  top: 50%;
}
.gallery-scroller li img,
.gallery-scroller li .hover-info,
.gallery-scroller li .hover-info > a {
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.gallery-scroller.thumbs-lg li {
  width: 480px;
  height: 480px;
}

.gallery .item {
  position: relative;
  overflow: hidden;
  margin-bottom: 24px;
}
.gallery .item:hover img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
.gallery .item .hover-info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgba(86, 125, 140, 0.7);
  z-index: 1;
  text-align: center;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.gallery .item .hover-info > a {
  position: relative;
  top: 25%;
  -webkit-transform: translateY(-87%);
  -moz-transform: translateY(-87%);
  -ms-transform: translateY(-87%);
  -o-transform: translateY(-87%);
  transform: translateY(-87%);
}
.gallery .item:hover .hover-info {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.gallery .item:hover .hover-info > a {
  top: 50%;
}
.gallery .item img {
  width: 100%;
}
.gallery .item img,
.gallery .item .hover-info,
.gallery .item .hover-info > a {
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.gallery .pagination {
  margin: 55px 0;
}

/* #Blog
================================================== */
.gx-6 {
  --bs-gutter-x: 4rem;
}

.page-header {
  margin: 0;
  padding-top: 120px;
  padding-bottom: 60px;
  position: relative;
  background-color: #8eaeba;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  background-repeat: no-repeat;
  color: #fff;
}
.page-header::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #3d5963;
  z-index: 0;
  -khtml-opacity: 0.75;
  -moz-opacity: 0.75;
  opacity: 0.75;
}
.page-header.section-divider-bottom-* {
  padding-bottom: 140px !important;
}
.page-header .page-title {
  color: #fff;
  float: none;
  text-align: center;
  margin: 0 auto;
  position: relative;
  padding-bottom: 40px;
  font-family: "Playfair Display", serif;
}
.page-header .page-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 189px;
  max-width: 189px;
  height: 24px;
  background-repeat: no-repeat;
  background-position-x: center;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='189' height='26' viewBox='0 0 189 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M86.19%2C2.362L96.98%2C13%2C86.19%2C23.634%2C75.4%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M94.985%2C2.362L105.775%2C13%2C94.985%2C23.634%2C84.2%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%23fff' stroke-width='1.5px' d='M103.78%2C2.362L114.57%2C13%2C103.78%2C23.634%2C92.991%2C13Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M120%2C14V13H523v1H120Z'/%3E%3Cpath fill-rule='evenodd' fill='%23fff' d='M-331%2C14V13H69v1H-331Z'/%3E%3C/svg%3E");
  -khtml-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}

.page-content {
  padding: 80px 0;
}

.main {
  padding-bottom: 0;
}

.title,
.title-fancy,
.title-fancy-lg {
  float: none;
  text-align: center;
  margin: 0 auto 25px;
  font-size: 22pt;
  color: #73777b;
}
.title.color,
.title-fancy.color,
.title-fancy-lg.color {
  color: #8eaeba;
}

.title-fancy {
  font-family: "Playfair Display", serif;
  font-size: 30pt;
}

.title-fancy-lg {
  font-family: "Playfair Display", serif;
  font-size: 34pt;
}

.blog-listing .item,
.blog-listing .post-content,
.blog-main .item,
.blog-main .post-content {
  margin: 0 auto 70px;
  text-align: left;
  position: relative;
}
.blog-listing .item .image,
.blog-listing .post-content .image,
.blog-main .item .image,
.blog-main .post-content .image {
  width: 100%;
  overflow: hidden;
  position: relative;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.blog-listing .item .image img,
.blog-listing .post-content .image img,
.blog-main .item .image img,
.blog-main .post-content .image img {
  width: 100%;
  height: auto;
  -moz-transition: all 0.8s ease-out;
  -o-transition: all 0.8s ease-out;
  -webkit-transition: all 0.8s ease-out;
  transition: all 0.8s ease-out;
}
.blog-listing .item .image a,
.blog-listing .post-content .image a,
.blog-main .item .image a,
.blog-main .post-content .image a {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: block;
  background-color: rgba(86, 125, 140, 0.8);
  z-index: 9;
  text-align: center;
  visibility: hidden;
  text-decoration: none;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition: all 0.6s ease-out;
  -o-transition: all 0.6s ease-out;
  -webkit-transition: all 0.6s ease-out;
  transition: all 0.6s ease-out;
}
.blog-listing .item .image a .btn,
.blog-listing .post-content .image a .btn,
.blog-main .item .image a .btn,
.blog-main .post-content .image a .btn {
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: relative;
  top: 100%;
}
.blog-listing .item .image:hover img,
.blog-listing .post-content .image:hover img,
.blog-main .item .image:hover img,
.blog-main .post-content .image:hover img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
.blog-listing .item .image:hover > a,
.blog-listing .post-content .image:hover > a,
.blog-main .item .image:hover > a,
.blog-main .post-content .image:hover > a {
  visibility: visible;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.blog-listing .item .image:hover > a .btn,
.blog-listing .post-content .image:hover > a .btn,
.blog-main .item .image:hover > a .btn,
.blog-main .post-content .image:hover > a .btn {
  top: 49%;
}
.blog-listing .item .date,
.blog-listing .post-content .date,
.blog-main .item .date,
.blog-main .post-content .date {
  position: absolute;
  z-index: 10;
  top: -30px;
  left: calc(var(--bs-gutter-x) * 0.5);
  padding: 8px 35px;
  margin: 0 0 0 -20px;
  color: #fff;
  background-color: #8eaeba;
  border: 10px solid #fff;
  font-family: "Playfair Display", serif;
  font-size: 19pt;
}
.blog-listing .item .info-blog,
.blog-listing .post-content .info-blog,
.blog-main .item .info-blog,
.blog-main .post-content .info-blog {
  padding-top: 20px;
  text-align: center;
}
.blog-listing .item .info-blog .post-title,
.blog-listing .post-content .info-blog .post-title,
.blog-main .item .info-blog .post-title,
.blog-main .post-content .info-blog .post-title {
  padding-bottom: 15px;
  margin-bottom: 20px;
  font-size: 23pt;
  position: relative;
  text-align: left;
  width: 100%;
  text-align: center;
  padding-bottom: 35px;
  font-family: "Playfair Display", serif;
}
.blog-listing .item .info-blog .post-title::after,
.blog-listing .post-content .info-blog .post-title::after,
.blog-main .item .info-blog .post-title::after,
.blog-main .post-content .info-blog .post-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 189px;
  max-width: 189px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='189' height='26' viewBox='0 0 189 26'%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M86.19%2C2.362L96.98%2C13%2C86.19%2C23.634%2C75.4%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M94.985%2C2.362L105.775%2C13%2C94.985%2C23.634%2C84.2%2C13Z'/%3E%3Cpath fill='none' fill-rule='evenodd' stroke='%238eaeba' stroke-width='1.5px' d='M103.78%2C2.362L114.57%2C13%2C103.78%2C23.634%2C92.991%2C13Z'/%3E%3Cpath fill-rule='evenodd' fill='%238eaeba' d='M120%2C14V13H523v1H120Z'/%3E%3Cpath fill-rule='evenodd' fill='%238eaeba' d='M-331%2C14V13H69v1H-331Z'/%3E%3C/svg%3E");
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  -khtml-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
}
.blog-listing .item .info-blog .post-title a,
.blog-listing .post-content .info-blog .post-title a,
.blog-main .item .info-blog .post-title a,
.blog-main .post-content .info-blog .post-title a {
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.blog-listing .item .info-blog .post-title a:hover,
.blog-listing .post-content .info-blog .post-title a:hover,
.blog-main .item .info-blog .post-title a:hover,
.blog-main .post-content .info-blog .post-title a:hover {
  -khtml-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.blog-listing .item .info-blog .post-title small,
.blog-listing .post-content .info-blog .post-title small,
.blog-main .item .info-blog .post-title small,
.blog-main .post-content .info-blog .post-title small {
  display: block;
  font-style: italic;
  margin-top: 4px;
}
.blog-listing .item .info-blog .extra-padding,
.blog-listing .post-content .info-blog .extra-padding,
.blog-main .item .info-blog .extra-padding,
.blog-main .post-content .info-blog .extra-padding {
  padding-left: 50px;
  padding-right: 50px;
}
.blog-listing .item .bottom-info,
.blog-listing .post-content .bottom-info,
.blog-main .item .bottom-info,
.blog-main .post-content .bottom-info {
  margin: 20px 0 0;
  border-top: 1px solid #e2e2e2;
  padding: 18px 0 0;
  text-align: left;
  display: inline-block;
  width: 100%;
}
.blog-listing .item .bottom-info.center,
.blog-listing .post-content .bottom-info.center,
.blog-main .item .bottom-info.center,
.blog-main .post-content .bottom-info.center {
  text-align: center;
}
.blog-listing .item .blog-meta,
.blog-listing .post-content .blog-meta,
.blog-main .item .blog-meta,
.blog-main .post-content .blog-meta {
  padding: 0;
  margin: 0;
  float: left;
  margin-bottom: 10px;
}
.blog-listing .item .blog-meta li,
.blog-listing .post-content .blog-meta li,
.blog-main .item .blog-meta li,
.blog-main .post-content .blog-meta li {
  display: inline-block;
  margin-right: 14px;
  color: #8eaeba;
}
.blog-listing .item .blog-meta li:last-child,
.blog-listing .post-content .blog-meta li:last-child,
.blog-main .item .blog-meta li:last-child,
.blog-main .post-content .blog-meta li:last-child {
  margin-right: 0px;
}
.blog-listing .item .blog-meta li i,
.blog-listing .post-content .blog-meta li i,
.blog-main .item .blog-meta li i,
.blog-main .post-content .blog-meta li i {
  margin-right: 2px;
}
.blog-listing .item .blog-meta li a,
.blog-listing .post-content .blog-meta li a,
.blog-main .item .blog-meta li a,
.blog-main .post-content .blog-meta li a {
  font-style: italic;
  color: #8eaeba;
}
.blog-listing .item .blog-share,
.blog-listing .post-content .blog-share,
.blog-main .item .blog-share,
.blog-main .post-content .blog-share {
  float: right;
  margin-left: 10px;
}
.blog-listing .item .blog-share a,
.blog-listing .post-content .blog-share a,
.blog-main .item .blog-share a,
.blog-main .post-content .blog-share a {
  margin-left: 10px;
  padding: 0 5px;
  -khtml-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  -moz-transition: opacity 0.3s ease-out;
  -o-transition: opacity 0.3s ease-out;
  -webkit-transition: opacity 0.3s ease-out;
  transition: opacity 0.3s ease-out;
}
.blog-listing .item .blog-share a:hover,
.blog-listing .post-content .blog-share a:hover,
.blog-main .item .blog-share a:hover,
.blog-main .post-content .blog-share a:hover {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.blog-listing .item .center .blog-meta,
.blog-listing .post-content .center .blog-meta,
.blog-main .item .center .blog-meta,
.blog-main .post-content .center .blog-meta {
  float: none;
}
.blog-listing .item .center .blog-share,
.blog-listing .post-content .center .blog-share,
.blog-main .item .center .blog-share,
.blog-main .post-content .center .blog-share {
  float: none;
  margin: 0;
}
.blog-listing .item .center .blog-share a,
.blog-listing .post-content .center .blog-share a,
.blog-main .item .center .blog-share a,
.blog-main .post-content .center .blog-share a {
  margin: 0 5px;
}
.blog-listing .item.disabled,
.blog-listing .post-content.disabled,
.blog-main .item.disabled,
.blog-main .post-content.disabled {
  display: none;
}

.section-bg-color .blog-listing .item .date,
.section-bg-color .blog-listing .post-content .date,
.section-bg-color .blog-main .item .date,
.section-bg-color .blog-main .post-content .date {
  border-color: #f9f9f9;
}

.blog-main .post-content .image:hover img {
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
}
.blog-main .post-content .date {
  left: 0;
}
.blog-main .post-content .info-blog {
  padding-top: 45px;
  text-align: left;
}
.blog-main .post-content .info-blog.center {
  text-align: center;
}
.blog-main .post-content .info-blog .post-title {
  padding-bottom: 60px;
  margin-bottom: 40px;
}
.blog-main .post-content h3, .blog-main .post-content h4, .blog-main .post-content h5, .blog-main .post-content h6 {
  color: #42344F;
}
.blog-main .post-content p {
  margin: 20px 0;
}
.blog-main .post-content p.no-margin {
  margin: 0;
}
.blog-main .post-content blockquote {
  background-color: #f7f7f7;
  padding: 65px;
  font-size: 16pt;
  font-style: italic;
  color: #464646;
  position: relative;
  z-index: 0;
  overflow: hidden;
  margin: 40px auto;
}
.blog-main .post-content blockquote::before {
  content: "";
  width: 510px;
  height: 479px;
  position: absolute;
  top: -32px;
  right: -65px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-medium.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-transform: rotate(-6deg) scaleX(-1);
  -moz-transform: rotate(-6deg) scaleX(-1);
  -ms-transform: rotate(-6deg) scaleX(-1);
  -o-transform: rotate(-6deg) scaleX(-1);
  transform: rotate(-6deg) scaleX(-1);
  -khtml-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
}
.blog-main .post-content .alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em;
  margin-bottom: 1.5em;
}
.blog-main .post-content .alignright {
  display: inline;
  float: right;
  margin-left: 1.5em;
  margin-bottom: 1.5em;
}
.blog-main .post-content .aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.blog-main .post-content .fullwidth {
  width: 100%;
}
.blog-main h1, .blog-main h2, .blog-main h3, .blog-main h4, .blog-main h5, .blog-main h6 {
  margin-bottom: 30px;
}
.blog-main h1 {
  font-size: 24pt;
}
.blog-main h2 {
  font-size: 21pt;
}
.blog-main h3 {
  font-size: 17pt;
}
.blog-main h4 {
  font-size: 13pt;
}
.blog-main h5 {
  font-size: 12pt;
}
.blog-main h6 {
  font-size: 10pt;
}
.blog-main .inner-title {
  text-transform: uppercase;
  font-size: 18pt;
  margin-bottom: 40px;
  font-family: "Playfair Display", serif;
}

.pagination {
  text-align: center;
  padding: 0;
  margin: 20px 0 100px;
  display: flex;
  width: 100%;
  justify-content: center;
}
.pagination ul {
  list-style: none;
  display: inline-block;
  padding: 0;
  margin: 0;
}
.pagination ul > li {
  display: inline-block;
  width: 50px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  border-right: 1px solid #d9d9d9;
  margin-bottom: 5px;
}
.pagination ul > li a {
  display: block;
  width: 100%;
  height: 48px;
  color: #73777b;
  text-align: center;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.pagination ul > li a:hover, .pagination ul > li a:focus {
  text-decoration: none;
}
.pagination ul li:last-child {
  border-right: 0;
}
.pagination ul li a:hover,
.pagination ul li a.active {
  color: #8eaeba;
}
.pagination ul#pages {
  margin: 0 15px;
  order: 2;
}
.pagination ul#previous {
  order: 1;
  margin-left: 10px;
}
.pagination ul#next {
  order: 3;
  margin-right: 10px;
}
.pagination ul#previous li,
.pagination ul#next li {
  width: 37px;
  border: 1px solid #8eaeba;
}
.pagination ul#previous li a,
.pagination ul#next li a {
  color: #8eaeba;
}
.pagination ul#previous li a:hover,
.pagination ul#next li a:hover {
  background-color: #8eaeba;
  color: #fff;
}

.comments {
  margin-bottom: 50px;
}
.comments .comment-list {
  list-style: none;
  width: 100%;
  padding: 0;
  margin: 0;
}
.comments .comment-list > li {
  width: 100%;
}
.comments .comment-list > li .comment-content {
  display: flex;
  border-top: 1px solid #d9d9d9;
  padding-top: 26px;
}
.comments .comment-list > li .comment-avatar {
  width: 85px;
  height: 85px;
  margin-right: 20px;
  margin-bottom: 20px;
  position: relative;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  overflow: hidden;
}
.comments .comment-list > li .comment-avatar img {
  width: 100%;
  height: auto;
}
.comments .comment-list > li ul {
  list-style: none;
  padding: 0;
  margin-left: 105px;
}
.comments .comment-list > li .comment,
.comments .comment-list > li .comment-info {
  width: calc(100% - 105px);
  position: relative;
  margin-bottom: 22px;
}
.comments .comment-list > li .comment h4,
.comments .comment-list > li .comment-info h4 {
  margin-bottom: 25px;
  font-weight: 300;
  position: relative;
  text-transform: uppercase;
}
.comments .comment-list > li .comment h4 small,
.comments .comment-list > li .comment-info h4 small {
  display: block;
  font-size: 11pt;
  margin-top: 3px;
  text-transform: none;
  color: #73777b;
}
.comments .comment-list > li .comment p,
.comments .comment-list > li .comment-info p {
  margin: 0;
}
.comments .comment-list > li .comment .reply-btn,
.comments .comment-list > li .comment-info .reply-btn {
  float: right;
  margin-bottom: 14px;
  position: relative;
  z-index: 1;
  font-size: 13pt;
  text-transform: uppercase;
}
.comments .comment-list > li:first-child > .comment-content {
  border: none;
  padding-top: 0;
}

.comment-form {
  margin-bottom: 80px;
}
.comment-form .form-check-wrapper {
  color: #73777b;
}

.sidebar {
  padding-bottom: 60px;
}

/* #Widgets
================================================== */
.widget {
  margin-bottom: 60px;
}
.widget .widget-title {
  font-family: "Playfair Display", serif;
  text-transform: uppercase;
}
.widget h2.widget-title {
  font-size: 18pt;
  margin-bottom: 24px;
}

.widget-about {
  text-align: center;
}
.widget-about .image-container {
  position: relative;
  padding: 5px 22px 70px;
}
.widget-about .image-container::before, .widget-about .image-container::after {
  content: "";
  width: 293px;
  height: 200px;
  position: absolute;
  top: -56px;
  left: 0;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-large.svg);
  background-repeat: no-repeat;
  background-size: contain;
}
.widget-about .image-container::after {
  top: auto;
  left: auto;
  right: 0;
  bottom: 0;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.widget-about .image-container .image {
  position: relative;
  overflow: hidden;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  width: 100%;
}
.widget-about .image-container .image::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 15px rgba(255, 255, 255, 0.4);
  -moz-box-shadow: inset 0 0 0 15px rgba(255, 255, 255, 0.4);
  box-shadow: inset 0 0 0 15px rgba(255, 255, 255, 0.4);
}
.widget-about h2 {
  font-size: 37pt;
  margin-bottom: 30px;
}

.widget-search form {
  position: relative;
}
.widget-search form button {
  padding: 12px 15px;
  background: none;
  border: 0;
  position: absolute;
  top: 1px;
  right: 1px;
  color: #8eaeba;
  font-size: 15pt;
  background-color: #fdfdfd;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.widget-search form button:hover {
  background-color: #8eaeba;
  color: #fff;
}
.widget-search form button:hover ~ input {
  border-color: #8eaeba;
}
.widget-search form input {
  width: 100%;
  background-color: #fdfdfd;
}
.widget-search form input:focus, .widget-search form input:active {
  outline: none;
}

.widget-categories > ul {
  list-style: none;
  padding: 0;
}
.widget-categories > ul li {
  margin: 8px 0;
}
.widget-categories > ul li a {
  color: #73777b;
  display: inline-block;
}
.widget-categories > ul li a span {
  color: #adb2b6;
}
.widget-categories > ul li a:hover span {
  color: #8eaeba;
}
.widget-categories > ul li:hover:before {
  color: #8eaeba;
}

.widget-latest-posts > ul {
  list-style: none;
  display: inline-block;
  padding: 0;
  clear: both;
}
.widget-latest-posts > ul > li {
  display: inline-block;
  margin-bottom: 26px;
  width: 100%;
  border-bottom: 0 !important;
}
.widget-latest-posts > ul > li::before {
  display: none;
}
.widget-latest-posts > ul > li .image {
  position: relative;
  float: left;
  width: 30%;
  margin-right: 3%;
}
.widget-latest-posts > ul > li .image.no-thumb {
  background-color: #e3e3e3;
  height: 100px;
}
.widget-latest-posts > ul > li .image img {
  width: 100%;
  height: auto;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.widget-latest-posts > ul > li .image a {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: block;
  background-color: rgba(86, 125, 140, 0.8);
  z-index: 9;
  text-align: center;
  visibility: hidden;
  text-decoration: none;
  color: #fff;
  text-align: center;
  font-size: 16pt;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.widget-latest-posts > ul > li .image a span {
  position: relative;
  top: 100%;
  display: block;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.widget-latest-posts > ul > li .image:hover > a {
  visibility: visible;
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.widget-latest-posts > ul > li .image:hover > a span {
  top: 49%;
}
.widget-latest-posts > ul .top-info {
  border-bottom: 1px solid #E4E4E4;
  font-size: 11pt;
  font-style: italic;
  float: right;
  width: 67%;
  margin: 0 0 11px;
  padding: 0 0 5px;
}
.widget-latest-posts > ul .top-info li {
  display: inline-block;
  margin-right: 14px;
  float: none;
  padding: 0;
  color: #adb2b6;
}
.widget-latest-posts > ul .top-info li:last-child {
  margin-right: 0px;
}
.widget-latest-posts > ul .top-info li i {
  margin-right: 5px;
}
.widget-latest-posts > ul .top-info li a {
  color: #73777b;
}
.widget-latest-posts > ul .top-info li a:hover {
  color: #8eaeba;
}
.widget-latest-posts > ul h3 {
  font-family: "Poppins", sans-serif;
  font-size: 16pt;
  display: inline-block;
  margin-bottom: 15px;
  width: 67%;
}
.widget-tags .tags a {
  color: #73777b;
  display: inline-block;
  font-style: italic;
  margin: 0 9px 2px 0;
  text-transform: uppercase;
}
.widget-tags .tags span {
  -khtml-opacity: 0.25;
  -moz-opacity: 0.25;
  opacity: 0.25;
}

.widget-newsletter {
  background-color: #8eaeba;
  color: #fff;
  padding: 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}
.widget-newsletter::before {
  content: "";
  width: 600px;
  height: 410px;
  position: absolute;
  top: 0px;
  right: -240px;
  z-index: 1;
  background-position: center center;
  background-image: url(../images/flower-large-dark.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-transform: rotate(-24deg);
  -moz-transform: rotate(-24deg);
  -ms-transform: rotate(-24deg);
  -o-transform: rotate(-24deg);
  transform: rotate(-24deg);
  -khtml-opacity: 0.1;
  -moz-opacity: 0.1;
  opacity: 0.1;
}
.widget-newsletter h2 {
  margin-bottom: 20px;
}
.widget-newsletter .neela-style {
  --offset: 10px;
  --border-size: 1px;
  color: #fff;
  position: relative;
  padding: 22px 30px;
  z-index: 1;
}
.widget-newsletter .neela-style .h-lines {
  border-top-color: rgba(255, 255, 255, 0.5);
  border-bottom-color: rgba(255, 255, 255, 0.5);
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.widget-newsletter .neela-style .v-lines {
  border-left-color: rgba(255, 255, 255, 0.5);
  border-right-color: rgba(255, 255, 255, 0.5);
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.widget-newsletter p {
  margin-bottom: 35px;
}
.widget-newsletter form {
  position: relative;
  color: #73777b;
}
.widget-newsletter form button {
  padding: 12px 15px;
  background: none;
  border: 0;
  position: absolute;
  top: 1px;
  right: 1px;
  color: #8eaeba;
  background-color: #fdfdfd;
  font-size: 15pt;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.widget-newsletter form button:hover {
  background-color: #8eaeba;
  color: #fff;
}
.widget-newsletter form button:hover ~ input {
  border-color: #8eaeba;
}
.widget-newsletter form input {
  width: 100%;
  background-color: #fdfdfd;
  margin-bottom: 8px;
}
.widget-newsletter form input:focus, .widget-newsletter form input:active {
  outline: none;
}
.widget-newsletter.light {
  background-color: transparent;
  color: #73777b;
  padding: 0 30px;
}
.widget-newsletter.light::before {
  display: none;
}

.widget-contact-info {
  text-align: center;
}
.widget-contact-info img {
  max-width: 100%;
  margin-bottom: 25px;
}
.widget-contact-info .sn-icons {
  margin: 0;
}

.contact-info {
  list-style: none;
  padding: 0;
  margin: 0 0 25px;
}
.contact-info li {
  margin-bottom: 15px;
  font-style: italic;
}
.contact-info li span {
  text-transform: uppercase;
  font-style: normal;
  color: #464646;
}
.contact-info li a {
  color: #73777b;
}

.widget-quick-links {
  text-align: center;
}
.widget-quick-links > ul {
  list-style: none;
  padding: 0;
  margin: 0 0 15px;
}
.widget-quick-links > ul li {
  margin-bottom: 15px;
  letter-spacing: 1px;
}
.widget-quick-links > ul li a {
  color: #73777b;
}

/* #RSVP
================================================== */
#rsvp {
  background: linear-gradient(#fff 50%, #8eaeba 50%) no-repeat;
}
#rsvp::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  z-index: -1;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 210px 100vw 0 0;
  border-color: #fff transparent transparent transparent;
  margin-top: -1px;
}
#rsvp.section-bg-color {
  background: linear-gradient(#f9f9f9 50%, #8eaeba 50%) no-repeat;
}
#rsvp.section-bg-color::before {
  border-color: #f9f9f9 transparent transparent transparent;
}

#rsvp-2 {
  background: linear-gradient(#8eaeba 50%, #fff 50%) no-repeat;
}
#rsvp-2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  z-index: -1;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 210px 100vw 0 0;
  border-color: #8eaeba transparent transparent transparent;
  margin-top: -1px;
}
#rsvp-2.section-bg-color {
  background: linear-gradient(#8eaeba 50%, #f9f9f9 50%) no-repeat;
}
#rsvp-2.section-bg-color::before {
  border-color: #8eaeba transparent transparent transparent;
}

.form-wrapper {
  position: relative;
  padding: 40px 60px;
  margin-bottom: 40px;
  background: #fff;
  -webkit-box-shadow: 0px 0px 25px 10px rgba(100, 100, 100, 0.14);
  -moz-box-shadow: 0px 0px 25px 10px rgba(100, 100, 100, 0.14);
  box-shadow: 0px 0px 25px 10px rgba(100, 100, 100, 0.14);
}
.form-wrapper.overflow {
  top: -120px;
  margin-bottom: -270px;
}
.form-wrapper.no-shadow {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.form-wrapper.flowers::before, .form-wrapper.flowers::after {
  content: "";
  width: 857px;
  height: 585px;
  position: absolute;
  top: -240px;
  right: -45px;
  z-index: -1;
  background-position: center center;
  background-image: url(../images/flower-large.svg);
  background-repeat: no-repeat;
}
.form-wrapper.flowers::before {
  -khtml-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
}
.form-wrapper.flowers::after {
  top: auto;
  right: auto;
  bottom: -240px;
  left: -45px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  -khtml-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
}
.form-wrapper.neela-style {
  --offset: 11px;
  --border-size: 1px;
  border: 42px solid #fff;
}
.form-wrapper.neela-style > .h-lines {
  border-top-color: #8eaeba;
  border-bottom-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  z-index: 0;
}
.form-wrapper.neela-style > .v-lines {
  border-left-color: #8eaeba;
  border-right-color: #8eaeba;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  z-index: 0;
}
.form-wrapper form {
  position: relative;
  z-index: 1;
}
.form-wrapper .btn > .h-lines,
.form-wrapper .btn > .v-lines {
  z-index: 0;
}

.g-recaptcha {
  display: inline-block;
}

#rsvp-2 .form-wrapper.flowers::before {
  -khtml-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
}
#rsvp-2 .form-wrapper.flowers::after {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}

.form-control,
.form-select {
  background-color: #fbfbfb;
  border: 1px solid #e1e1e1;
  color: #73777b;
  height: auto;
  margin-bottom: 20px;
  padding: 11px 20px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -moz-transition: border-color 0.3s ease-out;
  -o-transition: border-color 0.3s ease-out;
  -webkit-transition: border-color 0.3s ease-out;
  transition: border-color 0.3s ease-out;
}

.form-control::-webkit-input-placeholder {
  color: #73777b;
}

.form-control:-moz-placeholder {
  color: #73777b;
}

.form-control::-moz-placeholder {
  color: #73777b;
}

.form-control:-ms-input-placeholder {
  color: #73777b;
}

.form-control:focus,
.form-select:focus {
  outline: 0 none;
  border-color: #8eaeba;
  background-color: #fbfbfb;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

textarea.form-control {
  min-height: 120px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23808080' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}

.form-check-input:active {
  filter: none;
  background-color: rgba(142, 174, 186, 0.25);
}

.form-check-input:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.form-check-input:checked {
  background-color: #8eaeba;
}

.form-check-input[type=radio] {
  border-color: #8eaeba;
}

.form-check-input:checked[type=radio] {
  background-color: #fff;
  border-color: #8eaeba;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%238eaeba'/%3e%3c/svg%3e");
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.form-check-input[type=checkbox] {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  border-color: #8eaeba;
}

.form-check-input.is-invalid {
  border-color: #dc3545;
}

.form-check-input.is-invalid ~ label,
.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #dc3545;
}

.form-check-input.is-invalid:focus,
.was-validated .form-check-input:invalid:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.form-check-wrapper {
  color: #8eaeba;
  margin-bottom: 20px;
}
.form-check-wrapper > label {
  color: #73777b;
  margin-bottom: 5px;
  display: block;
}
.form-check-wrapper > .form-check:last-child {
  margin-right: 0;
}

.alert {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}

.btn-close:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

/* #Footer
================================================== */
#footer-onepage {
  text-align: left;
  padding: 0;
  position: relative;
}
#footer-onepage .footer-widget-area {
  position: relative;
  text-align: center;
  width: 100%;
  padding: 15px 0;
  z-index: 0;
  background-color: transparent;
}
#footer-onepage .footer-widget-area:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.15);
}
#footer-onepage .footer-widget-area .footer-info {
  display: flex;
  height: 100%;
  align-items: center;
  font-style: italic;
}
#footer-onepage .footer-widget-area .footer-info.left {
  text-align: left;
  justify-content: left;
}
#footer-onepage .footer-widget-area .footer-info.right {
  text-align: right;
  justify-content: right;
}
#footer-onepage .copyright {
  text-align: center;
  padding: 15px 0;
  background-color: transparent;
}

.footer-logo {
  font-family: "Playfair Display", serif;
  font-size: 30pt;
  line-height: 0.65;
  padding: 26px 64px;
  position: relative;
  display: inline-block;
}
.footer-logo::before, .footer-logo::after {
  content: "";
  width: 100px;
  height: 87px;
  position: absolute;
  bottom: 22px;
  right: -12px;
  z-index: -1;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url(../images/flower-small.svg);
  background-size: contain;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  -webkit-transform: rotate(50deg);
  -moz-transform: rotate(50deg);
  -ms-transform: rotate(50deg);
  -o-transform: rotate(50deg);
  transform: rotate(50deg);
}
.footer-logo::after {
  top: auto;
  right: auto;
  left: -12px;
  -webkit-transform: rotate(-130deg);
  -moz-transform: rotate(-130deg);
  -ms-transform: rotate(-130deg);
  -o-transform: rotate(-130deg);
  transform: rotate(-130deg);
}
.footer-logo small {
  font-size: 12pt;
  position: relative;
  top: -8px;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}

#footer {
  background-color: #f9f9f9;
  position: relative;
}
#footer .widget-title {
  color: #464646;
  text-transform: none;
}
#footer .footer-widget-area {
  padding: 60px 0 0;
}
#footer .copyright {
  text-align: center;
  background-color: transparent;
}
#footer .copyright > .container {
  border-top: 1px solid #ddd;
  padding-top: 23px;
  padding-bottom: 29px;
}
.sn-icons {
  list-style: none;
  padding: 0;
  display: inline-block;
  line-height: 1;
}
.sn-icons li {
  display: inline-block;
  margin: 0;
  padding: 0;
}
.sn-icons a {
  display: inline-block;
  font-size: 19pt;
  overflow: hidden;
  padding: 0 5px;
  margin: 0 1px;
  color: #8eaeba;
  text-align: center;
  background: none !important;
  -khtml-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
.sn-icons a:hover {
  -khtml-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}

/* #Lightbox
================================================== */
.lb-disable-scrolling {
  overflow: hidden;
}

.lightboxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: #000;
  display: none;
  width: 100%;
  height: 100%;
  -khtml-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}

.lightbox {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 10000;
  text-align: center;
  line-height: 0;
  font-weight: normal;
  outline: none;
}
.lightbox .lb-image {
  display: block;
  height: auto;
  max-width: inherit;
  max-height: none;
  border: 10px solid white;
}
.lightbox a img {
  border: none;
}

.lb-outerContainer {
  position: relative;
  *zoom: 1;
  width: 250px;
  height: 250px;
  margin: 0 auto;
  /* Background color behind image.
     This is visible during transitions. */
  background-color: white;
}

.lb-outerContainer:after {
  content: "";
  display: table;
  clear: both;
}

.lb-loader {
  position: absolute;
  top: 43%;
  left: 0;
  height: 25%;
  width: 100%;
  text-align: center;
  line-height: 0;
}

.lb-cancel {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;
  background: url(../images/loading.gif) no-repeat;
}

.lb-nav {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 10;
}

.lb-container > .nav {
  left: 0;
}

.lb-nav a {
  outline: none;
}

.lb-prev,
.lb-next {
  padding: 6px 10px;
  cursor: pointer;
  display: block;
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-weight: 900;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-size: 36pt;
  position: absolute;
  top: 50%;
  border: 5px solid transparent;
  -khtml-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.lb-nav a.lb-prev {
  left: -20px;
  color: #fff;
}
.lb-nav a.lb-prev::before {
  content: "";
}

.lb-nav a.lb-prev:hover,
.lb-nav a.lb-next:hover {
  -khtml-opacity: 1 !important;
  -moz-opacity: 1 !important;
  opacity: 1 !important;
}

.lb-nav a.lb-next {
  right: -20px;
  color: #fff;
}
.lb-nav a.lb-next::before {
  content: "";
}

.lb-nav:hover a.lb-prev {
  left: 20px;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.lb-nav:hover a.lb-next {
  right: 20px;
  -khtml-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}

.lb-dataContainer {
  margin: 0 auto;
  padding-top: 10px;
  *zoom: 1;
  width: 100%;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.lb-dataContainer::after {
  content: "";
  display: table;
  clear: both;
}

.lb-data {
  padding: 0 4px;
  color: #ccc;
}
.lb-data .lb-details {
  width: 85%;
  float: left;
  text-align: left;
  line-height: 1.1em;
}
.lb-data .lb-caption {
  font-size: 13px;
  font-weight: bold;
  line-height: 1em;
}
.lb-data .lb-caption a {
  color: #4ae;
}
.lb-data .lb-number {
  display: none !important;
  clear: left;
  padding-bottom: 1em;
  font-size: 12px;
  color: #999999;
}
.lb-data .lb-close {
  display: block;
  float: right;
  width: 40px;
  height: 40px;
  text-align: right;
  outline: none;
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-weight: 900;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-size: 18pt;
  text-align: center;
  line-height: 30px;
  margin-right: -4px;
  margin-top: -7px;
  border: 5px solid transparent;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.lb-data .lb-close::before {
  content: "";
}
.lb-data .lb-close:hover {
  cursor: pointer;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* #Media Queries
================================================== */
@media (max-width: 1399px) {
  .invite .invite_info h2 {
    font-size: 30pt;
  }
  .invite .invite_info .date {
    font-size: 20pt;
  }

  .invite .invite_title {
    font-size: 70pt;
  }
  .invite .invite_title .text {
    line-height: 55px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .element .image .hover-info h3 {
    margin-top: 0;
    margin-bottom: 18px;
    font-size: 26pt;
  }

  .side-flowers-light::before {
    left: -80px;
  }
  .side-flowers-light::after {
    right: -80px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .widget h2.section-title {
    font-size: 32pt;
  }
}
@media (max-width: 1199px) {
  .hero-title {
    font-size: 54pt;
  }
  .hero-title small {
    font-size: 28pt;
  }

  .hero-wrapper {
    margin-top: 100px;
  }
  .hero-wrapper h1.hero-title {
    margin-bottom: 28px;
  }
  .hero-wrapper .hero-subtitle span {
    font-size: 42pt;
  }
  .hero-wrapper.v-center {
    margin-top: auto;
  }

  .countdown > div > div {
    font-size: 45pt;
  }

  .blog-bottom-info > ul {
    width: 100%;
    border-right: none;
  }

  .bmaid-gmen .image .hover-info h3,
.bmaid-gmen-color .image .hover-info h3 {
    font-size: 20pt;
  }

  .side-flowers-light::before {
    left: -145px;
  }
  .side-flowers-light::after {
    right: -145px;
  }

  .side-flowers::before {
    left: -430px;
  }

  .side-flowers::after {
    right: -430px;
  }

  .overflow-image {
    width: 46%;
  }
}
@media (max-width: 991px) {
  .navbar-nav {
    width: auto;
  }

  .nav.navbar-nav > li {
    display: none;
  }

  .navbar > a.btn {
    display: none;
  }

  #nav-mobile-btn {
    display: block;
  }

  .hero-wrapper {
    margin-top: 120px;
  }
  .hero-wrapper::before {
    left: -45px;
  }
  .hero-wrapper::after {
    right: -45px;
  }
  .hero-wrapper h2 {
    font-size: 16pt;
  }
  .hero-wrapper.v-center {
    margin-top: auto;
  }

  .hero-title {
    font-size: 44pt;
    margin-bottom: 50px;
  }
  .hero-title span:first-child:before {
    left: -82px;
    top: -24px;
  }
  .hero-title span:last-child:after {
    right: -82px;
    top: -24px;
  }
  .hero-title small {
    font-size: 24pt;
  }

  .hero-wrapper .hero-subtitle {
    font-size: 16pt;
  }
  .hero-wrapper .hero-subtitle span {
    font-size: 36pt;
  }

  .countdown > div > div {
    font-size: 36pt;
  }

  .gallery-container .description-wrapper {
    grid-column: 2/24;
    grid-row: 2;
    margin-top: 25px;
  }
  .gallery-container .timeline-gallery-wrapper {
    grid-column: 1/-1;
  }
  .gallery-container:hover .description {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }

  .invite .invite_title {
    font-size: 80pt;
  }
  .invite .invite_title .text {
    padding: 90px 60px;
  }
  .invite .invite_title .text::before, .invite .invite_title .text::after {
    width: 127px;
    height: 112px;
  }
  .invite .invite_title .text::before {
    top: 20px;
    right: 18px;
  }
  .invite .invite_title .text::after {
    bottom: 0;
  }
  .invite .invite_title .text small {
    font-size: 25px;
  }
  .invite .invite_info h2 {
    font-size: 28pt;
  }
  .invite .invite_info .date {
    font-size: 20pt;
  }

  .map-info-container .info-wrapper {
    grid-column: 5/-5;
    grid-row: 2;
    margin-top: 3%;
  }
  .map-info-container .map-wrapper {
    grid-column: 1/-1;
    grid-row: 1;
  }
  .map-info-container .location-info.open {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }

  h1.section-title {
    font-size: 36pt;
  }

  .padding-divider-top {
    padding-top: 100px;
  }

  .section-divider-top-1 {
    padding-top: 100px !important;
  }
  .section-divider-top-1.off-section {
    height: 90px;
  }
  .section-divider-top-1::before {
    border-width: 90px 100vw 0 0;
  }

  .section-divider-top-2 {
    padding-top: 100px !important;
  }
  .section-divider-top-2.off-section {
    height: 90px;
  }
  .section-divider-top-2::before {
    border-width: 0 0 90px 100vw;
  }

  .section-divider-bottom-1 {
    padding-bottom: 100px !important;
  }
  .section-divider-bottom-1.off-section {
    height: 90px;
  }
  .section-divider-bottom-1::after {
    border-width: 0 0 90px 100vw;
  }

  .section-divider-bottom-2 {
    padding-bottom: 100px !important;
  }
  .section-divider-bottom-2.off-section {
    height: 90px;
  }
  .section-divider-bottom-2::after {
    border-width: 90px 0 0 100vw;
  }

  .section-title-xl {
    font-size: 70pt;
  }
  .section-title-xl small {
    font-size: 40pt;
  }

  .wedding-gifts li > div > i {
    font-size: 35pt;
    top: 0px;
  }
  .wedding-gifts li h3 {
    font-size: 20pt;
  }
  .wedding-gifts li .btn {
    padding: 8px 20px 9px 21px;
    font-size: 0.9rem;
  }

  .gallery-scroller li {
    height: 250px;
    width: 250px;
  }
  .gallery-scroller.thumbs-lg li {
    height: 320px;
    width: 320px;
  }

  .blog-listing .item .info-blog .extra-padding {
    padding: 0;
  }

  .blog-listing .item .date,
.blog-listing .post-content .date,
.blog-main .item .date,
.blog-main .post-content .date {
    font-size: 15pt;
    padding: 6px 20px;
  }

  .half-img {
    position: relative;
    height: 350px;
    width: 100%;
    background-attachment: fixed;
  }

  .overflow-image-text {
    width: 38%;
  }

  .overflow-image {
    width: 55%;
    top: -100px;
    bottom: -125px;
  }

  .side-flowers::before {
    left: -470px;
  }

  .side-flowers::after {
    right: -470px;
  }

  .form-wrapper.overflow {
    top: 50px;
    margin-bottom: -80px;
  }

  .footer-logo {
    font-size: 22pt;
    line-height: 0.85;
  }
  .footer-logo small {
    top: -4px;
  }
  .footer-logo::before, .footer-logo::after {
    width: 80px;
    height: 71px;
    bottom: 26px;
    right: 10px;
  }
  .footer-logo::after {
    right: auto;
    left: 10px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .element .image .hover-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 24pt;
  }
  .element .image .hover-info h3 small {
    font-size: 13pt;
  }

  .timeline .template-1 .date {
    right: 14%;
  }
  .timeline .template-1 .description-wrapper {
    grid-column: 2/span 13;
  }
  .timeline .template-2 .date {
    top: 60px;
    right: 10%;
  }

  .widget-about .image-container {
    margin: 0 100px;
  }

  .widget-newsletter.light {
    padding: 0;
  }
}
@media (max-width: 767px) {
  .hero-title {
    font-size: 40pt;
  }
  .hero-title span:first-child:before {
    left: -70px;
    top: -20px;
  }
  .hero-title span:last-child:after {
    right: -70px;
    top: -20px;
  }
  .hero-title small {
    font-size: 18pt;
  }

  .hero-subtitle {
    font-size: 13pt;
  }

  .hero-wrapper {
    position: relative;
  }
  .hero-wrapper::before, .hero-wrapper::after {
    content: "";
    width: 219px;
    height: 395px;
  }
  .hero-wrapper.x-pad {
    padding: 105px 0;
  }
  .hero-wrapper .hero-title {
    font-size: 32pt;
    margin-bottom: 0;
  }
  .hero-wrapper h2 {
    font-size: 15pt;
  }
  .hero-wrapper .hero-subtitle {
    font-size: 15pt;
  }
  .hero-wrapper .hero-subtitle span {
    font-size: 32pt;
    top: 6px;
  }
  .hero-wrapper h1.hero-title:after {
    bottom: -22px;
  }

  #about-us {
    margin-top: 0;
    background: #fff;
  }

  .neela-quote {
    font-size: 18pt;
  }

  .element .highlight {
    color: #73777b;
  }

  .timeline .template-1 .date {
    top: 18px;
    right: 4%;
  }
  .timeline .template-1 .date .neela-style {
    font-size: 18pt;
  }
  .timeline .template-1 .description-wrapper {
    grid-column: 2/24;
    grid-row: 2;
    margin-top: -5%;
  }
  .timeline .template-2 .date {
    top: 50px;
    right: 3%;
  }
  .timeline .template-2 .video {
    grid-column: 1/span 16;
    grid-row: 1;
  }
  .timeline .template-2 .image-1 {
    padding-top: 50%;
  }
  .timeline .template-2 .image-2 {
    grid-column: 2/span 12;
    padding-top: 65%;
  }
  .timeline .template-2 .description-wrapper {
    grid-column: 2/24;
    grid-row: 2;
    margin-top: -5%;
  }

  .timeline_footer .punchline {
    font-size: 44pt;
  }
  .timeline_footer .punchline small {
    font-size: 16pt;
  }

  .invite {
    flex-direction: column;
    margin: 25px 25px 60px;
  }
  .invite .invite_title {
    width: 100%;
  }
  .invite .invite_info {
    width: 100%;
  }

  .map-info-container .info-wrapper {
    grid-column: 3/-3;
    margin-top: 15%;
  }

  .bmaid-gmen .image .hover-info .content,
.bmaid-gmen-color .image .hover-info .content {
    padding: 10px;
  }
  .bmaid-gmen .image .hover-info h3,
.bmaid-gmen-color .image .hover-info h3 {
    font-size: 18pt;
    margin-bottom: 5px;
  }
  .bmaid-gmen .image .hover-info h3 small,
.bmaid-gmen-color .image .hover-info h3 small {
    font-size: 10pt;
  }

  .countdown > div > div {
    font-size: 30pt;
  }

  .countdown > div > span {
    font-size: 10pt;
  }

  .comments .comment-list > li .comment-content {
    display: block;
  }
  .comments .comment-list > li .comment-content .comment {
    width: 100%;
  }

  .comments .comment-list > li ul {
    margin-left: 55px;
  }

  .element-v2 {
    display: flex;
    flex-direction: column;
  }
  .element-v2 .image {
    width: 100%;
    display: block;
    order: 1;
  }
  .element-v2 .image img {
    display: block !important;
  }
  .element-v2 .info {
    width: 100%;
    display: block;
    order: 2;
  }

  .wedding-details::after {
    content: "";
    right: 20%;
    bottom: 0;
    height: 1px;
    width: 60%;
  }

  .overflow-image-text {
    width: 100%;
  }

  .overflow-image {
    width: 100%;
    position: relative;
    height: 560px;
    top: auto;
    bottom: -120px;
    margin-top: -80px;
  }

  .side-flowers::before {
    left: -500px;
  }

  .side-flowers::after {
    right: -500px;
  }

  #footer-onepage .footer-widget-area {
    padding: 25px 0;
  }
  #footer-onepage .footer-widget-area .footer-info.left,
#footer-onepage .footer-widget-area .footer-info.right {
    text-align: center;
    justify-content: center;
  }

  .footer-logo {
    margin: 15px 0;
  }
}
@media (max-width: 576px) {
  .padding-divider-top {
    padding-top: 70px;
  }

  .section-divider-top-1 {
    padding-top: 70px !important;
  }
  .section-divider-top-1.off-section {
    height: 60px;
  }
  .section-divider-top-1::before {
    border-width: 60px 100vw 0 0;
  }

  .section-divider-top-2 {
    padding-top: 70px !important;
  }
  .section-divider-top-2.off-section {
    height: 60px;
  }
  .section-divider-top-2::before {
    border-width: 0 0 60px 100vw;
  }

  .section-divider-bottom-1 {
    padding-bottom: 70px !important;
  }
  .section-divider-bottom-1.off-section {
    height: 60px;
  }
  .section-divider-bottom-1::after {
    border-width: 0 0 60px 100vw;
  }

  .section-divider-bottom-2 {
    padding-bottom: 70px !important;
  }
  .section-divider-bottom-2.off-section {
    height: 60px;
  }
  .section-divider-bottom-2::after {
    border-width: 60px 0 0 100vw;
  }

  .bmaid-gmen .image .hover-info .content,
.bmaid-gmen-color .image .hover-info .content {
    padding: 30px;
  }
  .bmaid-gmen .image .hover-info h3,
.bmaid-gmen-color .image .hover-info h3 {
    font-size: 32pt;
    margin-bottom: 35px;
  }
  .bmaid-gmen .image .hover-info h3 small,
.bmaid-gmen-color .image .hover-info h3 small {
    font-size: 12pt;
  }

  .side-flowers-light::before {
    left: -200px;
  }
  .side-flowers-light::after {
    right: -200px;
  }

  .side-flowers::before {
    left: -535px;
  }

  .side-flowers::after {
    right: -535px;
  }

  .gallery-scroller.thumbs-lg li {
    height: 250px;
    width: 250px;
  }
}
@media (max-width: 480px) {
  .hero-wrapper::before {
    left: -100px;
  }

  .hero-wrapper::after {
    right: -100px;
  }

  .hero-title {
    font-size: 30pt;
    margin-bottom: 15px;
  }
  .hero-title span:first-child:before {
    left: -52px;
    top: -10px;
  }
  .hero-title span:last-child:after {
    right: -52px;
    top: -10px;
  }
  .hero-title small {
    font-size: 16pt;
  }

  .hero-text {
    font-size: 22pt;
  }

  .element .image .hover-info h3 {
    font-size: 24pt;
  }

  .map-info-container .info-wrapper {
    grid-column: 2/-2;
    margin-top: 15%;
  }

  .countdown > div {
    padding: 0 12px;
  }
  .countdown > div > div {
    font-size: 26pt;
  }

  h1.section-title {
    font-size: 28pt;
  }

  h1.page-title {
    font-size: 28pt;
  }

  .section-desc h1 {
    font-size: 30pt;
  }

  .section-title-lg {
    font-size: 50pt;
  }
  .section-title-lg small {
    font-size: 30pt;
  }

  .timeline .template-1 .date {
    top: -10px;
    padding: 20px 16px;
  }
  .timeline .template-1 .date .neela-style {
    font-size: 12pt;
    padding: 6px 10px;
  }
  .timeline .template-2 .date {
    top: 30px;
    padding: 20px 16px;
  }
  .timeline .template-2 .date .neela-style {
    font-size: 12pt;
    padding: 6px 10px;
  }

  .timeline_footer .punchline {
    font-size: 34pt;
  }
  .timeline_footer .punchline small {
    font-size: 14pt;
  }

  .map-info-container .info-wrapper .location-info h4 {
    font-size: 18pt;
  }
  .map-info-container .info-wrapper .location-info h5 {
    font-size: 13pt;
  }

  .wedding-gifts li .neela-style {
    padding: 15px;
  }
  .wedding-gifts li > div > i {
    font-size: 28pt;
    top: 3px;
  }
  .wedding-gifts li h3 {
    font-size: 14pt;
  }

  .form-wrapper {
    padding: 20px 25px;
  }

  .pagination {
    display: inline-block;
  }
  .pagination ul#pages {
    order: 0;
    margin: 0 0 15px;
    width: 100%;
  }
  .pagination ul#pages li {
    float: none;
  }
  .pagination ul#previous,
.pagination ul#next {
    order: 0;
  }
  .pagination ul#previous li,
.pagination ul#next li {
    float: none;
  }

  .form-wrap .btn[type=submit] {
    white-space: normal;
  }
  .form-wrap .btn[type=submit] .icon {
    display: none;
  }

  .submit_form .icon {
    display: none !important;
  }
}
@media (max-width: 1199px) {
  .parallax-background {
    background-attachment: scroll !important;
  }
}
@supports (-webkit-touch-callout: none) {
  .background-video {
    position: absolute;
  }
}

/*# sourceMappingURL=style.css.map */
