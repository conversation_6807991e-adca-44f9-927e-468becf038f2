/* 350 of 3689 CSS properties converted by rtl.daskhat.ir 
 These properties should be override originals. 
 just load rtl css file after original css file.*/ 

body {
	direction:rtl;
	unicode-bidi: embed;
}

.section-title-lg small, .section-title-xl small{
    margin-left: -10px;
	margin-right: 0;
}

.btn > i{
    margin-left: 10px;
	margin-right: 0;
}

.btn > i.after{
    margin-left: 0;
    margin-right: 10px;
}

.btn.only-icon > i{
    margin-left: 0;
}


.owl-carousel .owl-item .item .hover-img{
    right: 0;
	left: auto;
}

.owl-carousel .owl-nav{
    right: -15px;
	left: auto;
}

.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev{
    background: right  0;
}

.owl-carousel .owl-nav button.owl-prev{
    margin-left: 10px;
	margin-right: 0;
}

.owl-carousel .owl-dots{
    right: 0;
	left: auto;
}

.owl-carousel .owl-video-play-icon{
    right: 50%;
	left: auto;
    margin-right: -40px;
	margin-left: 0;
}

.section-divider-top-1.off-section{
    right: 0;
	left: auto;
}

.section-divider-top-1::before{
    right: 0;
	left: auto;
    border-width: 130px 0 0 100vw ;
    border-color: #fff transparent transparent transparent ;
}

.section-divider-top-1.section-divider-bg-color::before{
    border-color: #f9f9f9 transparent transparent transparent ;
}

.section-divider-top-2.off-section{
    right: 0;
	left: auto;
}

.section-divider-top-2::before{
    right: 0;
	left: auto;
    border-width: 0 100vw 130px 0 ;
    border-color: transparent transparent #fff transparent ;
}

.section-divider-top-2.section-divider-bg-color::before{
    border-color: transparent transparent #f9f9f9 transparent ;
}

.section-divider-bottom-1.off-section{
    right: 0;
	left: auto;
}

.section-divider-bottom-1::after{
    right: 0;
	left: auto;
    border-width: 0 100vw 130px 0 ;
    border-color: transparent transparent #fff transparent ;
}

.section-divider-bottom-1.section-divider-bg-color::after{
    border-color: transparent transparent #f9f9f9 transparent ;
}

.section-divider-bottom-2.off-section{
    right: 0;
	left: auto;
}

.section-divider-bottom-2::after{
    right: 0;
	left: auto;
    border-width: 150px 100vw 0 0 ;
    border-color: transparent #fff transparent transparent ;
}

.section-divider-bottom-2.section-divider-bg-color::after{
    border-color: transparent #f9f9f9 transparent transparent ;
}

#header{
    right: 0;
	left: auto;
}

.nav-section{
    background: none repeat scroll right  0 #ffffff;
}

.nav-section .nav-logo{
    float: right;
}

.navbar{
    float: left;
}

.navbar-nav > li.dropdown > a .caret{
    border-left: 3px solid rgba(0, 0, 0, 0);
    margin-right: 10px;
	margin-left: 0;
    border-right: 4px solid transparent;
}

.navbar-nav .dropdown-menu{
    right: 50%;
	left: auto;
    margin-right: -80px;
	margin-left: 0;
}

.navbar-nav .dropdown-menu > li > a::before{
    right: 7%;
	left: auto;
}

.navbar-nav .dropdown-submenu > .dropdown-menu{
    right: 100%;
	left: auto;
    margin-right: -1px;
	margin-left: 0;
}

.navbar-nav .dropdown-submenu > .dropdown-menu > li > a::before{
    right: 7%;
	left: auto;
}

.navbar-nav .dropdown-submenu > a:after{
    float: left;
    border-width: 4px 4px 4px 0 ;
    border-right-color: #73777b;
    left: 8px;
	right: auto;
}

.navbar-nav .dropdown-submenu:hover > a:after{
    border-right-color: #6ab19c;
}

.navbar-nav .dropdown-submenu.pull-left > .dropdown-menu{
    right: -100%;
	left: auto;
    margin-right: 10px;
	margin-left: 0;
}

#nav-mobile-btn{
    float: left;
}

.nav-mobile{
    left: 0;
	right: auto;
    border-right: 5px solid rgba(106, 177, 156, 0.3);
}

.nav-mobile > i{
    left: 15px;
	right: auto;
}

.nav-mobile h2 i{
    margin-left: 10px;
	margin-right: 0;
}

.nav-mobile > ul > li{
    padding-right: 10px;
}

.nav-mobile > ul ul{
    padding-right: 15px;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
}

.nav-mobile::after{
    left: 0;
	right: auto;
}

.stuck{
    right: 0;
	left: auto;
}

.stuck #mobile-nav{
    margin-left: 10%;
	margin-right: 0;
}

#hero::before{
    right: 0;
	left: auto;
}

.hero-wrapper h1.hero-title:after{
    right: 50%;
	left: auto;
    margin-right: -50px;
	margin-left: 0;
}

.hero-divider-top::before, .hero-divider-bottom::before{
    right: 0;
	left: auto;
}

.zs-enabled .zs-slideshow, .zs-enabled .zs-slides, .zs-enabled .zs-slide{
    right: 0;
	left: auto;
}

.zs-enabled .zs-slideshow .zs-slides .zs-slide{
    background: transparent none no-repeat 50% 50%;
}

.zs-enabled .zs-slideshow .zs-bullets{
    right: 0;
	left: auto;
}

.zs-enabled .zs-slideshow:after{
    right: 0;
	left: auto;
    background: transparent none repeat right  0;
}

.countdown > div{
    border-left: 1px solid rgba(255, 255, 255, 0.5);
	border-right: 0;
}

.countdown > div:last-child{
    border-left: none;
}

.free-wall{
    right: 0;
	left: auto;
}

.free-wall .item{
    float: right;
}

.background-video{
    right: 50%;
	left: auto;
}

#about-us{
    background: linear-gradient(to bottom, #fff, #fff) no-repeat right 0px 365px;
}

.element.offset-xl-2{
	margin-left: 0;
}

.element .image::after{
    right: 0;
    left: 0;
}

.element .image .hover-info{
    right: 0;
	left: auto;
    text-align: right;
}

.element .image .hover-info .v-lines{
    border-right-color: #6ab19c;
    border-left-color: #6ab19c;
}

.divider-about-us{
    right: 50%;
	left: auto;
    margin-right: -52.5px;
	margin-left: 0;
}

.neela-quote::before, .neela-quote::after{
    right: 0;
	left: auto;
}

.element-v2 .image{
    background-position: 50% 50%;
}

.element-v2 .info{
    border-right-width: 42px;
    border-left-width: 42px;
}

.element-v2 .info .v-lines{
    border-right-color: #6ab19c;
    border-left-color: #6ab19c;
}

.element-v2::before{
    left: -112px;
	right: auto;
	-webkit-transform: scaleX(1) rotate(-16deg);
	-moz-transform: scaleX(1) rotate(-16deg);
	-ms-transform: scaleX(1) rotate(-16deg);
	-o-transform: scaleX(1) rotate(-16deg);
	transform: scaleX(1) rotate(-16deg);
}

.element-v2.photo-right::before{
    left: auto;
    right: -110px;
	-webkit-transform: scaleX(1) rotate(165deg);
	-moz-transform: scaleX(1) rotate(165deg);
	-ms-transform: scaleX(1) rotate(165deg);
	-o-transform: scaleX(1) rotate(165deg);
	transform: scaleX(1) rotate(165deg);
}

.timeline::before{
    right: 50%;
	left: auto;
    margin-right: -1px;
	margin-left: 0;
}

.timeline .date{
    left: 19%;
	right: auto;
}

.timeline .year .neela-style,
.timeline .date .neela-style{
	display:inline-block;
}

.timeline .description h4::after{
    right: 0;
	left: auto;
}

.timeline .description h4.has-icon{
    padding-right: 45px;
	padding-left: 0;
}

.timeline .description h4.has-icon i{
    right: -8px;
	left: auto;
}

.timeline .template-2 .date{
    left: 15%;
	right: auto;
}

.gallery-container::after{
    right: -165px;
	left: auto;
	-webkit-transform: scaleY(1) rotate(90deg);
	-moz-transform: scaleY(1) rotate(90deg);
	-ms-transform: scaleY(1) rotate(90deg);
	-o-transform: scaleY(1) rotate(90deg);
	transform: scaleY(1) rotate(90deg);
}

.gallery-container:hover .description{
	-webkit-transform: translateX(-35%);
	-moz-transform: translateX(-35%);
	-ms-transform: translateX(-35%);
	-o-transform: translateX(-35%);
	transform: translateX(-35%);
}

.invite .neela-style .v-lines{
    border-right-color: #fff;
    border-left-color: #fff;
}

.invite .invite_title .text{
    text-align: right;
}

.invite .invite_title .text span:last-child{
    margin-right: 24px;
	margin-left: 0;
}

.invite .invite_info .date::before, .invite .invite_info .date::after{
    right: 0;
	left: auto;
}

.overflow-image{
    left: 0;
	right: auto;
}

.overflow-image.flower::before{
    left: -274px;
	right: auto;
	-webkit-transform: rotate(-45deg) scaleX(-1);
	-moz-transform: rotate(-45deg) scaleX(-1);
	-ms-transform: rotate(-45deg) scaleX(-1);
	-o-transform: rotate(-45deg) scaleX(-1);
	transform: rotate(-45deg) scaleX(-1);
}

.menu-section::before{
    left: 0;
	right: auto;
}

.menu-wrapper .menu-items li:after{
    right: 50%;
	left: auto;
    margin-right: -25px;
	margin-left: 0;
}

.half-img{
    background-position: 50% 50%;
    right: 0;
	left: auto;
}

.map-info-container::before{
    right: 50%;
	left: auto;
    margin-right: -1px;
	margin-left: 0;
}

.map-info-container .location-info.open{
	-webkit-transform: translateX(25%);
	-moz-transform: translateX(25%);
	-ms-transform: translateX(25%);
	-o-transform: translateX(25%);
	transform: translateX(25%);
}

.map-info-container .info-wrapper .location-info h4.has-icon{
    padding-right: 72px;
}

.map-info-container .info-wrapper .location-info h4.has-icon i{
    right: 0px;
	left: auto;
}

.map-info-container .info-wrapper .location-info .info-map-divider::after{
    right: 0;
	left: auto;
}

.marker::after{
    right: 11px;
	left: auto;
    border-width: 12px 9px 0 9px ;
    border-color: #ffffff transparent transparent transparent ;
    _border-color: #ffffff #000000 #000000 #000000 ;
}

.marker::before{
    right: 10px;
	left: auto;
    border-width: 12px 10px 0 10px ;
    border-color: #6ab19c transparent transparent transparent ;
    _border-color: #6ab19c #000000 #000000 #000000 ;
}

.marker:hover::after{
    border-color: #6ab19c transparent transparent transparent ;
}

.marker:hover::before{
    border-color: #ffffff transparent transparent transparent ;
}

.map_pins{
    left: 0;
	right: auto;
}

.map_pins .pins li i, .map_pins .pins li .fa{
    margin-left: 5px;
	margin-right: 0;
}

.wedding-details::after{
    left: 0;
	right: auto;
}

.map_pins_full .pins li i, .map_pins_full .pins li .fa{
    margin-left: 9px;
	margin-right: 0;
}

.bmaid-gmen-color .image .hover-info .v-lines{
    border-right-color: #fff;
    border-left-color: #fff;
}

.testimonials .owl-item .item blockquote::before, .testimonials .owl-item .item blockquote::after{
    right: 0;
	left: auto;
}

.testimonials .owl-item .item blockquote::after{
    right: auto;
    left: 0;
}

.testimonials .owl-item .item .author h3 small::before{
    right: 50%;
	left: auto;
    margin-right: -30px;
	margin-left: 0;
}

.wedding-gifts li .neela-style .v-lines{
    border-right-color: rgba(106, 177, 156, 0.5);
    border-left-color: rgba(106, 177, 156, 0.5);
}

.wedding-gifts li > div > i{
    right: 0;
	left: auto;
    text-align: left;
    margin-left: 7%;
	margin-right: 0;
}

.wedding-gifts li h3{
    left: 0;
	right: auto;
}

.wedding-gifts li:hover .neela-style .v-lines{
    border-right-color: #6ab19c;
    border-left-color: #6ab19c;
}

.progress-wrapper label small{
	float:left;
}

.gallery-scroller li{
    margin: -5px 0 0 -3px ;
}

.gallery-scroller li .hover-info{
    right: 0;
	left: auto;
}

.gallery .item .hover-info{
    right: 0;
	left: auto;
}

.page-header::before{
    right: 0;
	left: auto;
}

.blog-listing .item, .blog-listing .post-content, .blog-main .item, .blog-main .post-content{
    text-align: right;
}

.blog-listing .item .image a, .blog-listing .post-content .image a, .blog-main .item .image a, .blog-main .post-content .image a{
    right: 0;
	left: auto;
}

.blog-listing .item .date, .blog-listing .post-content .date, .blog-main .item .date, .blog-main .post-content .date{
    right: calc(var(--bs-gutter-x) * 0.5);
	left: auto;
    margin: 0 -20px 0 0 ;
}

.blog-listing .item .info-blog .extra-padding, .blog-listing .post-content .info-blog .extra-padding, .blog-main .item .info-blog .extra-padding, .blog-main .post-content .info-blog .extra-padding{
    padding-right: 50px;
    padding-left: 50px;
}

.blog-listing .item .bottom-info, .blog-listing .post-content .bottom-info, .blog-main .item .bottom-info, .blog-main .post-content .bottom-info{
    text-align: right;
}

.blog-listing .item .blog-meta, .blog-listing .post-content .blog-meta, .blog-main .item .blog-meta, .blog-main .post-content .blog-meta{
    float: right;
}

.blog-listing .item .blog-meta li, .blog-listing .post-content .blog-meta li, .blog-main .item .blog-meta li, .blog-main .post-content .blog-meta li{
    margin-left: 14px;
	margin-right: 0;
}

.blog-listing .item .blog-meta li:last-child, .blog-listing .post-content .blog-meta li:last-child, .blog-main .item .blog-meta li:last-child, .blog-main .post-content .blog-meta li:last-child{
    margin-left: 0px;
}

.blog-listing .item .blog-meta li i, .blog-listing .post-content .blog-meta li i, .blog-main .item .blog-meta li i, .blog-main .post-content .blog-meta li i{
    margin-left: 2px;
	margin-right: 0;
}

.blog-listing .item .blog-share, .blog-listing .post-content .blog-share, .blog-main .item .blog-share, .blog-main .post-content .blog-share{
    float: left;
    margin-right: 10px;
	margin-left: 0;
}

.blog-listing .item .blog-share a, .blog-listing .post-content .blog-share a, .blog-main .item .blog-share a, .blog-main .post-content .blog-share a{
    margin-right: 10px;
	margin-left: 0;
}

.blog-main .post-content .date{
    right: 0;
	left: auto;
}

.blog-main .post-content .info-blog{
    text-align: right;
}

.blog-main .post-content blockquote::before{
    left: -65px;
	right: auto;
}

.blog-main .post-content .alignleft{
    float: right;
    margin-left: 1.5em;
	margin-right: 0;
}

.blog-main .post-content .alignright{
    float: left;
    margin-right: 1.5em;
	margin-left: 0;
}

.pagination ul > li{
    border-left: 1px solid #d9d9d9;
	border-right: 0;
}

.pagination ul li:last-child{
    border-left: 0;
}

.pagination ul#previous{
    margin-right: 10px;
	margin-left: 0;
}

.pagination ul#next{
    margin-left: 10px;
	margin-right: 0;
}

.comments .comment-list > li .comment-avatar{
    margin-left: 20px;
	margin-right: 0;
}

.comments .comment-list > li ul{
    margin-right: 105px;
	margin-left: 0;
}

.comments .comment-list > li .comment .reply-btn, .comments .comment-list > li .comment-info .reply-btn{
    float: left;
}

.widget-about .image-container::before, .widget-about .image-container::after{
    right: 0;
	left: auto;
}

.widget-about .image-container::after{
    right: auto;
    left: 0;
}

.widget-about .image-container .image::before{
    right: 0;
	left: auto;
}

.widget-search form button{
    left: 1px;
	right: auto;
}

.widget-latest-posts > ul > li .image{
    float: right;
    margin-left: 3%;
	margin-right: 0;
}

.widget-latest-posts > ul > li .image a{
    right: 0;
	left: auto;
}

.widget-latest-posts > ul .top-info{
    float: left;
}

.widget-latest-posts > ul .top-info li{
    margin-left: 14px;
	margin-right: 0;
}

.widget-latest-posts > ul .top-info li:last-child{
    margin-left: 0px;
}

.widget-latest-posts > ul .top-info li i{
    margin-left: 5px;
	margin-right: 0;
}

.widget-tags .tags a{
    margin: 0 0 2px 9px ;
}

.widget-newsletter::before{
    left: -240px;
	right: auto;
}

.widget-newsletter .neela-style .v-lines{
    border-right-color: rgba(255, 255, 255, 0.5);
    border-left-color: rgba(255, 255, 255, 0.5);
}

.widget-newsletter form {
	text-align: right;
}	

.widget-newsletter form button{
    left: 1px;
	right: auto;
}

.form-wrapper.neela-style > .v-lines{
    border-right-color: #6ab19c;
    border-left-color: #6ab19c;
}

.form-check-wrapper > .form-check:last-child{
    margin-left: 0;
}

.form-check-inline{
	margin-right: 0;
}

#footer-onepage{
    text-align: right;
}

#footer-onepage .footer-widget-area:before{
    right: 0;
	left: auto;
}

#footer-onepage .footer-widget-area .footer-info.left{
    text-align: right;
}

#footer-onepage .footer-widget-area .footer-info.right{
    text-align: left;
}

.lightboxOverlay{
    right: 0;
	left: auto;
}

.lightbox{
    right: 0;
	left: auto;
}

.lb-loader{
    right: 0;
	left: auto;
}

.lb-data .lb-details{
    float: right;
    text-align: right;
}

.lb-data .lb-number{
    clear: right;
}

.lb-data .lb-close{
    float: left;
    margin-left: -4px;
	margin-right: 0;
}

@media (min-width: 1200px) and (max-width: 1399px){
    .side-flowers-light::before{
        right: -80px;
		left: auto;
    }

    .side-flowers-light::after{
        left: -80px;
		right: auto;
    }

}

@media (max-width: 1199px){
    .blog-bottom-info > ul{
        border-left: none;
    }

    .side-flowers-light::before{
        right: -145px;
		left: auto;
    }

    .side-flowers-light::after{
        left: -145px;
		right: auto;
    }

    .side-flowers::before{
        right: -430px;
		left: auto;
    }

    .side-flowers::after{
        left: -430px;
		right: auto;
    }

}

@media (max-width: 991px){
    .hero-title span:first-child:before{
        right: -82px;
		left: auto;
    }

    .hero-title span:last-child:after{
        left: -82px;
		right: auto;
    }

    .section-divider-top-1::before{
        border-width: 90px 0 0 100vw ;
    }

    .section-divider-top-2::before{
        border-width: 0 100vw 90px 0 ;
    }

    .section-divider-bottom-1::after{
        border-width: 0 100vw 90px 0 ;
    }

    .section-divider-bottom-2::after{
        border-width: 90px 100vw 0 0 ;
    }

    .wedding-gifts li .btn{
        padding: 8px 21px 9px 20px ;
    }

    .side-flowers::before{
        right: -470px;
		left: auto;
    }

    .side-flowers::after{
        left: -470px;
		right: auto;
    }

}

@media (min-width: 768px) and (max-width: 991px){
    .timeline .template-1 .date{
        left: 14%;
		right: auto;
    }

    .timeline .template-2 .date{
        left: 10%;
		right: auto;
    }

}

@media (max-width: 767px){
    .hero-title span:first-child:before{
        right: -70px;
		left: auto;
    }

    .hero-title span:last-child:after{
        left: -70px;
		right: auto;
    }

    .timeline .template-1 .date{
        left: 4%;
		right: auto;
    }

    .timeline .template-2 .date{
        left: 3%;
		right: auto;
    }

    .comments .comment-list > li ul{
        margin-right: 55px;
		margin-left: 0;
    }

    .wedding-details::after{
        left: 20%;
		right: auto;
    }

    .side-flowers::before{
        right: -500px;
		left: auto;
    }

    .side-flowers::after{
        left: -500px;
		right: auto;
    }

}

@media (max-width: 576px){
    .section-divider-top-1::before{
        border-width: 60px 0 0 100vw ;
    }

    .section-divider-top-2::before{
        border-width: 0 100vw 60px 0 ;
    }

    .section-divider-bottom-1::after{
        border-width: 0 100vw 60px 0 ;
    }

    .section-divider-bottom-2::after{
        border-width: 60px 100vw 0 0 ;
    }

    .side-flowers-light::before{
        right: -200px;
		left: auto;
    }

    .side-flowers-light::after{
        left: -200px;
		right: auto;
    }

    .side-flowers::before{
        right: -535px;
		left: auto;
    }

    .side-flowers::after{
        left: -535px;
		right: auto;
    }

}

@media (max-width: 480px){
    .hero-title span:first-child:before{
        right: -52px;
		left: auto;
    }

    .hero-title span:last-child:after{
        left: -52px;
		right: auto;
    }

    .location-info .event_left .event_panel .info-right, .location-info .event_right .event_panel .info-right{
        border-right: 0;
    }

}

