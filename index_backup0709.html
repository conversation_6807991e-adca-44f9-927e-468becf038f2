<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<meta charset="utf-8">
	
	<!-- Page Title -->
	<title>Flóra & Ádám</title>

	<!-- Open Graph / Facebook Metadata -->
	<meta property="og:title" content="Flóra & Ádám Esküvő" />
	<meta property="og:description" content="Szeretettel meghívunk az esküvőnkre! Itt tudsz visszajelezni." />
	<meta property="og:image" content="https://www.fladam.hu/favico.png" />
	<meta property="og:url" content="https://www.fladam.hu/" />
	<meta property="og:type" content="website" />
	<meta property="og:locale" content="hu_HU" />
	
	<!-- Optional Twitter Card (if needed) -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content="Flóra & Ádám Meghívó" />
	<meta name="twitter:description" content="Szeretettel meghívunk az esküvőnkre!" />
	<meta name="twitter:image" content="https://www.fladam.hu/favico.png" />
	
	<meta name="keywords" content="Flóra, Ádám, wedding, esküvő">
	<meta name="description" content="Flóra & Ádám wedding esküvő">
	<meta name="author" content="<EMAIL>">
	
	<!-- Mobile Meta Tag -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	
	<!-- Fav and touch icons -->
	<link rel="icon" href="images/fav_touch_icons/favicon.ico" sizes="any">
	<link rel="icon" href="images/fav_touch_icons/favicon.svg" type="image/svg+xml">
	<link rel="apple-touch-icon" href="images/fav_touch_icons/apple-touch-icon-180x180.png">
	<link rel="manifest" href="images/fav_touch_icons/manifest.json">
	
	<!-- IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
	  <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script> 
	<![endif]-->
	
	<!-- Google Web Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300&display=swap" rel="stylesheet">
	
	<!-- Bootstrap CSS -->
	<link href="css/bootstrap.min.css" rel="stylesheet" />
	
	<!-- FontAwesome CSS -->
	<link href="css/fontawesome-all.min.css" rel="stylesheet" />
	
	<!-- Neela Icon Set CSS -->
	<link href="css/neela-icon-set.css" rel="stylesheet" />
	
	<!-- Owl Carousel CSS -->
	<link href="css/owl.carousel.min.css" rel="stylesheet" />
	
	<!-- Template CSS -->
	<link href="css/style.css" rel="stylesheet" />
	
	<!-- Modernizr JS -->
	<script src="js/modernizr-3.6.0.min.js"></script>

	<link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
	<script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>

	<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>


	<style>
		.gallery-scroller li img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
			margin: 0;
			padding: 0;
		}
		#menu-image {
		background-image: url("photos/image28.jpg");
		background-size: cover;
		background-position: center;
		background-attachment: scroll;

		}

		.nav-section .nav-logo {
			display: flex;
			align-items: center;  /* Vertically center */
		}

		.nav-logo h1 {
			font-size: 24px;
			line-height: 1.2;
			margin: 0;
			padding: 0;
			font-family: 'Poppins', sans-serif;
			font-weight: 300;
			color: #fff;
		}

		.btn-waze {
			background-color: #33CCFF;
			color: #fff;
			font-family: 'Poppins', sans-serif;
			font-weight: 300;
			font-size: 16px;
			font-weight: 500;
			padding: 10px 20px;
			border-radius: 5px;
			border: 1px solid #8eaeba;
			cursor: pointer;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			text-decoration: none;
		}

		.btn-waze:hover {
			border: 1px solid #fff;
		}

		.map-info-container::before, .timeline::before {
			display: none;
		}

		.btn-danger {
			color: #ac2925;
		}

		.btn-danger:hover {
			background-color: #c9302c;
			color: #fff;
		}

		@keyframes fadeInUp {
			0% { opacity: 0; transform: translateY(10px); }
			100% { opacity: 1; transform: translateY(0); }
			}

			.guest-fieldset {
			animation: fadeInUp 0.4s ease;
			}

			.form-check {
				display: inline-block;
				margin-right: 10px;
				min-width: 100px;
			}

			.btn-custom {
				font-family: 'Poppins', sans-serif;
				font-weight: 300;
				font-weight: 500;
				border-radius: 5px;
				background-color: #475D65; /* #8eaeba; */
				color: #fff;
				cursor: pointer;
				display: inline-flex;
				align-items: center;
				justify-content: center;
			}

			.btn-add {
				background-color: #666447;
				color: #fff;
			}
	</style>
    
</head>
<body>

	<!-- BEGIN PRELOADER -->
	<div id="preloader">
		<div class="loading-heart">
			<svg viewBox="0 0 512 512" width="100">
				<path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z" />
			</svg>
			<div class="preloader-title">
				Flóra<br>
				<small>&</small><br>
				Ádám
			</div>
		</div>
	</div>
	<!-- END PRELOADER -->


	<!-- BEGIN WRAPPER -->
	<div id="wrapper">
	
		<!-- BEGIN HEADER -->
		<header id="header">
			<div class="nav-section light no-menu">
				<div class="container">
					<div class="row">
						<div class="col-sm-12">
							<a href="#hero" class="nav-logo"><h1>Flóra & Ádám</h1></a>
		
							<!-- BEGIN MAIN MENU -->
							<nav class="navbar">
								<a href="#location" class="btn btn-light scrollto">Location</a>
								<a href="#rsvp" class="btn btn-light scrollto">RSVP</a>
		
								<button id="nav-mobile-btn"><i class="fas fa-bars"></i></button><!-- Mobile menu button -->
							</nav>
							<!-- END MAIN MENU -->
		
						</div>
					</div>
				</div>
			</div>
		</header>
		<!-- END HEADER -->
		
		
		<!-- BEGIN HERO SECTION -->
		<div id="hero" class="bg-slideshow section-divider-bottom-1 section-divider-bg-color">
			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						<div class="v-center">
						<div class="hero-divider-top light" data-animation-direction="from-top" data-animation-delay="700"></div>
						
						<h1 class="hero-title light">
							<span data-animation-direction="from-right" data-animation-delay="300">Flóra</span>
							<small data-animation-direction="from-top" data-animation-delay="300">&</small> 
							<span data-animation-direction="from-left" data-animation-delay="300">Ádám</span>
						</h1>
						
						<div class="hero-divider-bottom light" data-animation-direction="from-bottom" data-animation-delay="700"></div>
						
						<div class="hero-subtitle light">Are getting Married in</div>
						
						<!-- 
                        Countdown container 
						Use the data attribute "date" to set the countdown date. 
						E.g.: data-date="2022/09/20 3:00 PM"
                        -->
						<div class="countdown" data-date="2025/09/13 3:00 PM"></div>

						<div data-animation-direction="fade" data-animation-delay="1000">
							<a href="#rsvp" class="btn btn-light scrollto">RSVP</a>
						</div>
					</div>
					</div>
				</div>
			</div>
		</div>
		<!-- END HERO SECTION -->
		
		
		<!-- BEGIN BRIDE & GROOM SECTION -->
		<section class="section-bg-color overflow-content-over no-padding-top">
		
			<div class="section-bg-color overflow-content no-padding">
				<div class="container">
					<div class="row">
						<div class="col overflow-image-wrapper">
						
							<div class="overflow-image-text extra-padding-top">
								<h2 class="title">We've decided that from now on, we'll put up with each other forever.</h2>
								<p class="center">Come laugh, cry, and eat cake with us!</p>
								<p>&nbsp;</p>
								<p>&nbsp;</p>
							</div>
							
							<div class="overflow-image flower">
								<img src="photos/image8.jpg" alt="Couple Photo">
							</div>
						</div>
					</div>
				</div>
			</div>
			
		</section>
		<!-- END BRIDE & GROOM SECTION -->

		<!-- BEGIN TIMELINE SECTION -->
		<section id="loveline" class="section-bg-color">
			<div class="container">
				<div class="row">
					<div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
						<div class="timeline">		
							<div class="template-1">
		
								<div class="image-1" data-parallax="-4" data-animation-direction="from-left"
									data-animation-delay="250">
									<img src="photos/image19.jpg" alt="">
								</div>
		
								<div class="image-2" data-parallax="6" data-animation-direction="from-right"
									data-animation-delay="250">
									<img src="photos/image13.jpg" alt="">
								</div>
		
								<div class="description-wrapper" data-parallax="-6" data-animation-direction="from-bottom"
									data-animation-delay="250">
									<div class="description" data-parallax="-6" data-animation-direction="from-bottom"
										data-animation-delay="250">
										<img src="photos/image17.jpg" alt="">
									</div>
								</div>
							</div>
						</div>		
					</div>
				</div>
			</div>
		</section>
		<!-- END TIMELINE SECTION -->
		
		
		<!-- BEGIN WEDDING INVITE SECTION -->
		<section id="the-wedding" class="parallax-background bg-color-overlay padding-divider-top">
			<div class="section-divider-top-1 section-divider-bg-color off-section"></div><!-- The class "section-divider-top-1" can also be applied to the tag <section>. In this case, it was added on a new <div> because the tag <section> have all pseudo elements (::after and ::before) in use. -->
			<div class="container">
				<!--<div class="row">
					<div class="col-sm-12">
						<h2 class="section-title light">We're getting married</h2>
					</div>
				</div>-->
				
				<div class="row">
					<div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2 center">
						<div class="invite neela-style" data-animation-direction="from-left" data-animation-delay="100">
							<div class="invite_title">
								<div class="text">
									Save<small>the</small>Date
								</div>
							</div>
							
							<div class="invite_info">
								<h2>Flóra <small>&</small> Ádám</h2>
								
								<div class="uppercase">Request the honor of your presence on their wedding day</div>
								<div class="date">September 13, 2025<small>at 14:30 pm</small></div>
								<div class="uppercase">Szokolya,<br>Királyréti Vadászkastély</div>
								
								<!--<h5>Reception to follow</h5>-->
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END WEDDING INVITE SECTION -->

		<!-- BEGIN TIMELINE SECTION -->
		<section id="loveline" class="section-bg-color">
			<div class="container">
				<div class="row">
					<div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
						<div class="timeline">
		
							<div class="template-1">
		
								<div class="image-1" data-parallax="-4" data-animation-direction="from-left"
									data-animation-delay="250">
									<a href="invite/image20.jpg" target="_blank">
										<img src="invite/image20.jpg" alt="">
									</a>
								</div>
		
								<div class="image-2" data-parallax="6" data-animation-direction="from-right"
									data-animation-delay="250">
									<a href="invite/image3.jpg" target="_blank">
										<img src="invite/image3.jpg" alt="">
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END TIMELINE SECTION -->

		<!-- BEGIN MENU SECTION -->
		<section class="menu-section no-padding-top no-padding-bottom">
			<div id="menu-image" class="half-img"></div>
		
			<div class="container">
				<div class="row">
					<div class="col-xl-6 offset-xl-6 col-lg-8 offset-lg-4 menu-wrapper">
						<div class="neela-style">
							<div class="menu-top-flowers"></div>
		
							<h2 class="section-title-lg">Schedule</h2>
		
							<ul class="menu-items">
								<li>
									<h3>14:30</h3>
									<h4>Guest arrival</h4>
								</li>
								<li>
									<h3>15:30</h3>
									<h4>Ceremony</h4>
								</li>
								<li>
									<h3>16:00</h3>
									<h4>Photos</h4>
								</li>
								<li>
									<h3>19:00</h3>
									<h4>Dinner</h4>
								</li>
								<li>
									<h3>23:00</h3>
									<h4>Cake</h4>
								</li>
								<li>
									<h3>0:00</h3>
									<h4>Midnight snack</h4>
								</li>
								<li>
									<h3>0:45</h3>
									<h4>Moshpit till dawn</h4>
								</li>
							</ul>
		
							<div class="menu-bottom-flowers"></div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END MENU SECTION -->

				<!-- BEGIN WEDDING DETAILS SECTION --
				<section id="wedding-details" class="bg-color">
					<div class="container">
						<div class="row">
							<div class="col-md-2 wedding-details light">
								&nbsp;
								</div>
							<div class="col-md-4 wedding-details light">
								<i class="icon-champagne-glasses"></i>
								<h4>Parking</h4>
								<p>Parking space at the venue is limited. You can also park for free in the lot across the road or at the Turista Szálló
								(Tourist Lodge) parking area just opposite.</p>
							</div>
				
							<div class="col-md-4 wedding-details light">
								<i class="icon-champagne-glasses"></i>
								<h4>Accomodation</h4>
								<p>We can provide accommodation for up to 100 guests. Please let us know in advance if you'd like to stay overnight!</p>
							</div>
						</div>
					</div>
				</section>
		<!-- END WEDDING DETAILS SECTION -->

		<!-- BEGIN WEDDING LOCATION SECTION -->
		<section id="location">
			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						<h2 class="section-title">Location</h2>
					</div>
				</div>
			</div>
		
			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-xl-10 offset-xl-1">
		
						<div class="map-info-container">
							<div class="info-wrapper" data-animation-direction="from-bottom" data-animation-delay="100">
								<div class="location-info">
									<div class="neela-style">		
										<h4 class="has-icon"><i
												class="icon-diamond-ring"></i>
												Királyréti Vadászkastély
											</h4>
										<h5>Szokolya, Királyrét, 2624</h5>
										<p>
											47.89533180751165, 18.97720805857283
										</p>
										<div class="center">
											<a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes&q=Királyréti%20Vadászkastély" class="btn btn-waze" style="background-color: #33CCFF; color: #fff">
												<img src="img/waze-icon.svg" alt="" style="width: 20px; height: 20px; margin-right: 5px;">
												Waze
											</a>
										</div>
										<div class="center">
											<a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank"
												class="btn btn-waze" style="background-color: #d23f31; color: #fff">
												<svg xmlns="http://www.w3.org/2000/svg" style="width: 20px; height: 20px; margin-right: 5px;" fill="currentColor" viewBox="0 0 24 24">
													<path
														d="M12 2C8.1 2 5 5.1 5 9c0 5.2 7 13 7 13s7-7.8 7-13c0-3.9-3.1-7-7-7zm0 9.5c-1.4 0-2.5-1.1-2.5-2.5S10.6 6.5 12 6.5s2.5 1.1 2.5 2.5S13.4 11.5 12 11.5z" />
												</svg>
												Google Maps
											</a>
										</div>
									</div>
								</div>
							</div>
		
							<div class="map-wrapper" data-animation-direction="fade" data-animation-delay="100">
								<div id="map_canvas"></div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						<h2 class="section-title">Accommodation</h2>
					</div>
				</div>
			</div>
		
			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-xl-10 offset-xl-1">
						<p class="cta">
							We can provide accommodation for up to 100 guests. Please let us know in advance if you'd like to
							stay
							overnight!
						</p>
					</div>
				</div>
			</div>
			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						<h2 class="section-title">Parking</h2>
					</div>
				</div>
			</div>
		
			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-xl-10 offset-xl-1">
						<p class="cta">
							Parking space at the venue is limited. You can also park for free in the lot across the road or at
							the
							Turista Szálló
							(Tourist Lodge) parking area just opposite.
						</p>
		
		
					</div>
				</div>
			</div>

			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-xl-10 offset-xl-1">
						<blockquote class="neela-quote" style="font-size: 16pt;">
							Housing prices have gone through the roof, but our love is rock solid. If you bring a little envelope instead of a
							blender, it might just cover a square meter!
						</blockquote>
					</div>
				</div>
			</div>


		</section>
		<!-- END WEDDING INFO SECTION -->

		<!-- BEGIN GALLERY SECTION -->
		<section id="gallery" class="parallax-background bg-color-overlay padding-divider-top">
			<div class="section-divider-top-1 off-section"></div>
			<!-- The class "section-divider-top-1" can also be applied to the tag <section>. In this case, it was added on a new <div> because the tag <section> have all pseudo elements (::after and ::before) in use. -->
			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						<h1 class="section-title light">Us</h1>
					</div>
				</div>
			</div>
		
			<div class="gallery-wrapper">
				<div class="gallery-left"><i class="fas fa-chevron-left"></i></div>
				<div class="gallery-right"><i class="fas fa-chevron-right"></i></div>

				
				<!--
				<div class="gallery-scroller">
					<ul>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image1.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image1.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image2.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image2.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image4.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image4.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image5.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image5.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image6.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image6.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image7.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image7.jpg" alt="" />
						</li>
					</ul>
					<ul>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image8.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image8.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image9.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image9.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image10.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image10.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image11.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image11.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image12.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image12.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image13.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image13.jpg" alt="" />
						</li>
					</ul>
					<ul>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image14.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image14.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image16.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image16.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image17.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image17.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image18.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image18.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image19.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image19.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image21.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image21.jpg" alt="" />
						</li>
					</ul>
					<ul>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image22.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image22.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image23.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image23.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image24.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image24.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image25.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image25.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image26.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image26.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image27.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image27.jpg" alt="" />
						</li>
					</ul>
					<ul>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image28.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image28.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image29.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image29.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image30.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image30.jpg" alt="" />
						</li>
						<li>
							<div class="hover-info"><a class="btn btn-light btn-sm only-icon" href="photos/image31.jpg"
									data-lightbox="WeddingPhotos" title="Wedding Photos"><i class="fa fa-link"></i></a></div><img
								src="photos/image31.jpg" alt="" />
						</li>
					</ul>
				</div>
				-->
				
				
				
			</div>
		</section>
		<!-- END GALLERY SECTION -->
		
	
		<!-- BEGIN CONTACTS SECTION --
		<section id="rsvp" class="section-bg-color extra-padding-section">
			<div class="container">
				
				<div class="row">
					<div class="col-lg-10 offset-lg-1 col-xl-8 offset-xl-2  col-xxl-6 offset-xxl-3">
						
						<div class="form-wrapper flowers neela-style">
							<h2 class="section-title">Will you Attend?</h2>
							
							<form id="form-rsvp" method="post" action="#">
								
								<div class="form-floating">
									<input type="text" name="Name" id="name" placeholder="Your Name*" class="form-control required fromName">
									<label for="name">Your Name*</label>
								</div>
								
								<div class="form-floating">
									<input type="email" name="E-mail" id="email" placeholder="E-mail*" class="form-control required fromEmail">
									<label for="email">E-mail*</label>
								</div>
								
								<div class="form-check-wrapper">
									<div class="form-check form-check-inline">
										<input class="form-check-input required" type="radio" name="Attend wedding" id="attend_wedding_yes">
										<label for="attend_wedding_yes">Yes, I will attend.</label>
									</div>
									
									<div class="form-check form-check-inline">
										<input class="form-check-input required" type="radio" name="Attend wedding" id="attend_wedding_no">
										<label for="attend_wedding_no">Sorry, I can't come.</label>
									</div>
								</div>
								
								<div class="form-floating">
									<select class="form-select" aria-label="Number of guests" name="Number of Guests" id="num_guests">
										<option value="0">0</option>
										<option value="1">1</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
										<option value="7">7</option>
									</select>
									
									<label for="num_guests">Number of guests</label>
								</div>

								<fieldset class="form-check-wrapper" id="meal_pref">
									<label>Do you follow any specific diet?</label>
								
									<div class="form-check">
										<input class="form-check-input" type="checkbox" value="lactose" id="diet_lactose" x-model="guest.diet">
										<label class="form-check-label" for="diet_lactose">
											Lactose-free
										</label>
									</div>
								
									<div class="form-check">
										<input class="form-check-input" type="checkbox" value="gluten" id="diet_gluten" x-model="guest.diet">
										<label class="form-check-label" for="diet_gluten">
											Gluten-free
										</label>
									</div>
								
									<div class="form-check">
										<input class="form-check-input" type="checkbox" value="sugar" id="diet_sugar" x-model="guest.diet">
										<label class="form-check-label" for="diet_sugar">
											Sugar-free
										</label>
									</div>
								
									<div class="form-check">
										<input class="form-check-input" type="checkbox" value="vegetarian" id="diet_vegetarian" x-model="guest.diet">
										<label class="form-check-label" for="diet_vegetarian">
											Vegetarian
										</label>
									</div>
								
									<div class="form-check">
										<input class="form-check-input" type="checkbox" value="vegan" id="diet_vegan" x-model="guest.diet">
										<label class="form-check-label" for="diet_vegan">
											Vegan
										</label>
									</div>
								
									<div class="form-floating mt-3">
										<input type="text" class="form-control" id="diet_other" placeholder="Other dietary restrictions"
											x-model="guest.diet_other">
										<label for="diet_other">Other:</label>
									</div>

									<div class="form-floating mb-2">
										<input type="text" class="form-control" placeholder="Any food allergies?" x-model="guest.allergy">
										<label>Do you have any food allergies?</label>
									</div>
								</fieldset>

								
								  

								<div class="form-floating mb-3">
									<input type="text" class="form-control" placeholder="Accommodation request" x-model="accommodation">
									<label>Would you like to request accommodation?</label>
								</div>
								
								<div class="form-floating mb-3">
									<textarea class="form-control" rows="4" placeholder="Special requests" x-model="special_notes"></textarea>
									<label>Is there anything else we should know about your needs?</label>
								</div>
								
								<div class="form_status_message"></div>
								
								<div class="center">
									<button type="submit" class="btn btn-primary submit_form">Send</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END CONTACTS SECTION -->

		<section id="rsvp" class="section-bg-color extra-padding-section">
			<div class="container">
				<div class="row">
					<div class="col-lg-10 offset-lg-1 col-xl-8 offset-xl-2 col-xxl-8 offset-xxl-2">
						<div class="form-wrapper flowers neela-style" x-data="rsvpForm">
							<h2 class="section-title" x-show="!success">Will you Attend?</h2>
		
							<form id="form-rsvp" @submit.prevent="submitForm" class="space-y-6" x-show="!success" x-transition:leave="transition ease-out duration-700"
								x-transition:leave-start="opacity-100 translate-y-0"
								x-transition:leave-end="opacity-0 translate-y-4">
		
								<!-- Guest fields -->
								<template x-for="(guest, index) in guests" :key="index">
									<fieldset class="guest-fieldset border p-4 rounded mb-3">
										<legend class="font-semibold mb-2">Guest #<span x-text="index + 1"></span></legend>
		
										<div class="form-floating mb-3">
											<input type="text" class="form-control" placeholder="Full name of attendee"
												x-model="guest.name" required>
											<label>Full name of attendee*</label>
										</div>

										<div class="mb-3">
											<p class="mb-2 fw-semibold">Do you follow any specific diet?</p>
										
											<div class="form-check">
												<input class="form-check-input" type="checkbox" :id="`diet_lactose_${index}`" value="lactose"
													x-model="guest.diet">
												<label class="form-check-label" :for="`diet_lactose_${index}`">
													Lactose-free
												</label>
											</div>
										
											<div class="form-check">
												<input class="form-check-input" type="checkbox" :id="`diet_gluten_${index}`" value="gluten"
													x-model="guest.diet">
												<label class="form-check-label" :for="`diet_gluten_${index}`">
													Gluten-free
												</label>
											</div>
										
											<div class="form-check">
												<input class="form-check-input" type="checkbox" :id="`diet_sugar_${index}`" value="sugar" x-model="guest.diet">
												<label class="form-check-label" :for="`diet_sugar_${index}`">
													Sugar-free
												</label>
											</div>
										
											<div class="form-check">
												<input class="form-check-input" type="checkbox" :id="`diet_vegetarian_${index}`" value="vegetarian"
													x-model="guest.diet">
												<label class="form-check-label" :for="`diet_vegetarian_${index}`">
													Vegetarian
												</label>
											</div>
										
											<div class="form-check">
												<input class="form-check-input" type="checkbox" :id="`diet_vegan_${index}`" value="vegan" x-model="guest.diet">
												<label class="form-check-label" :for="`diet_vegan_${index}`">
													Vegan
												</label>
											</div>
										
											<div class="form-check mt-2">
												<label class="form-check-label" :for="`diet_other_${index}`">
													Other:
												</label>
												<input type="text" class="form-control d-inline-block ms-2" style="width: 120px; height: 30px"
													:id="`diet_other_${index}`" x-model="guest.diet_other">
											</div>
										</div>
										  
		
										<div class="form-floating mb-2">
											<input type="text" class="form-control" placeholder="Any food allergies?"
												x-model="guest.allergy">
											<label>Do you have any food allergies?</label>
										</div>
		
										<div class="text-end">
											<button type="button" class="btn btn-danger btn-sm" @click="guests.splice(index, 1)"
												x-show="index > 0">
												✕ Remove Guest
											</button>
										</div>
									</fieldset>
								</template>
		
								<!-- Add guest button -->
								<div class="text-center mb-4">
									<button type="button" class="btn btn-primary btn-success btn-add btn-custom"
										@click="guests.push({name:'',diet:[],diet_other:'',allergy:''})">
										Add Guest
									</button>
								</div>
		
								<!-- Additional options -->
								<div class="form-check mb-3">
									<input class="form-check-input" type="checkbox" id="high_chair" x-model="high_chair">
									<label class="form-check-label" for="high_chair">
										I need a child chair.
									</label>
								</div>
		
								<div class="form-floating mb-3">
									<input type="email" class="form-control" placeholder="Your email address" x-model="contact"
										required>
									<label>Email*</label>
								</div>
		
								<div class="form-floating mb-3">
									<input type="text" class="form-control" placeholder="Accommodation request"
										x-model="accommodation">
									<label>Would you like to request accommodation?</label>
								</div>
		
								<div class="form-floating mb-3">
									<textarea class="form-control" rows="4" placeholder="Special requests"
										x-model="special_notes"></textarea>
									<label>Is there anything else we should know about your needs?</label>
								</div>
		
								<div class="form_status_message text-danger text-center" x-text="errorMessage"
									x-show="errorMessage"></div>
		
								<div class="center">
									<button type="submit" class="btn btn-primary btn-success btn-custom" :disabled="isSubmitting"
										x-text="isSubmitting ? 'Please wait...' : 'Submit'">
										Submit
									</button>
								</div>
							</form>

							<div class="successMessage" x-show="success" x-transition:enter="transition ease-out duration-700"
								x-transition:enter-start="opacity-0 translate-y-4"
								x-transition:enter-end="opacity-100 translate-y-0">
								<h2 class="section-title">Thank you for confirming your attendance.</h2>
								<h3>We can't wait to celebrate with you!</h3>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		  
		
		
		<!-- BEGIN FOOTER -->
		<footer id="footer">
		
			<div class="copyright">
				<div class="container">
					<div class="row">
						<div class="col-sm-12">
							&copy; 2025 Flóra & Ádám
						</div>
					</div>
				</div>
			</div>
		</footer>
		<!-- END FOOTER -->
		
	</div>
	<!-- END WRAPPER -->
	
	
	<!-- Google Maps API and Map Richmarker Library -->
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBHOXsTqoSDPQ5eC5TChvgOf3pAVGapYog"></script>
	<script src="js/richmarker.js"></script>
	
	<!-- Libs -->
	<script src="js/jquery-3.6.0.min.js"></script>
	<script src="js/jquery-ui.min.js"></script>
	<script src="js/jquery-migrate-3.3.2.min.js"></script>
	<script src="js/bootstrap.bundle.min.js"></script>
	<script src="js/jquery.placeholder.min.js"></script>
	<script src="js/ismobile.js"></script>
	<script src="js/retina.min.js"></script>
	<script src="js/waypoints.min.js"></script>
	<script src="js/waypoints-sticky.min.js"></script>
	<script src="js/owl.carousel.min.js"></script>
	<script src="js/lightbox.min.js"></script>
    
    <!-- Nicescroll script to handle gallery section touch slide -->
	<script src="js/jquery.nicescroll.js"></script>
    
    <!-- Hero Background Slideshow Script -->
	<script src="js/jquery.zoomslider.js"></script>
	
	<!-- Template Scripts -->
	<script src="js/variables.js"></script>
	<script src="js/scripts.js"></script>

	<script>
		mapboxgl.accessToken = 'pk.eyJ1IjoiYWRyYXl3ZWIiLCJhIjoiY2s2ZmVraG13MWtsZTNubXZqMnp1ZTJxdyJ9.PnvUTSep4Q8bTIcnNBH97A';

		const map = new mapboxgl.Map({
			container: 'map_canvas',
			style: 'mapbox://styles/mapbox/streets-v11',
			center: [18.97720805857283, 47.89533180751165],
			zoom: 15
		});

		new mapboxgl.Marker()
			.setLngLat([18.97720805857283, 47.89533180751165])
			.setPopup(new mapboxgl.Popup().setHTML('<strong>Királyréti Vadászkastély</strong>'))
			.addTo(map);
	</script>

<script>
	document.addEventListener('alpine:init', () => {
		Alpine.data('rsvpForm', () => ({
			guests: [{ name: '', diet: [], diet_other: '', allergy: '' }],
			high_chair: false,
			contact: '',
			accommodation: '',
			special_notes: '',
			isSubmitting: false,
			success: false,
			errorMessage: '',
			dietLabels: {
				lactose: 'Lactose-free',
				gluten: 'Gluten-free',
				sugar: 'Sugar-free',
				vegetarian: 'Vegetarian',
				vegan: 'Vegan'
			},
			submitForm() {
				this.isSubmitting = true;
				const guestData = this.guests.map(g => ({
					name: g.name,
					diet_lactose: g.diet.includes('lactose'),
					diet_gluten: g.diet.includes('gluten'),
					diet_sugar: g.diet.includes('sugar'),
					diet_vegetarian: g.diet.includes('vegetarian'),
					diet_vegan: g.diet.includes('vegan'),
					special_diet: g.diet_other,
					allergy: g.allergy
				}));

				const payload = {
					guests: guestData,
					contact: this.contact,
					accomodation: this.accommodation,
					special_request: this.special_notes,
					child_chair: this.high_chair,
					lang: 'en'
				};

				this.errorMessage = '';

				fetch('submit.php', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(payload)
				})
					.then(res => res.json())
					.then(data => {
						this.isSubmitting = false;
						if (data.success) {
							this.success = true;
						} else {
							this.errorMessage = data.error || 'Something went wrong during submission.';
						}
					})
					.catch(() => {
						this.isSubmitting = false;
						this.errorMessage = 'Something went wrong during submission.';
					});
			}
		}));
	});
</script>
  
	
</body>
</html>