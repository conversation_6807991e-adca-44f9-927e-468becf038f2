<div id="wrapper">
	
    <!-- BEGIN HEADER -->
    <header id="header">
        <div class="nav-section light no-menu">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12">
                        <a href="#hero" class="nav-logo"><h1>Flóra & Ádám</h1></a>
    
                        <!-- BEGIN MAIN MENU -->
                        <nav class="navbar">
                            <a href="#location" class="btn btn-sm btn-light scrollto"><PERSON><PERSON><PERSON><PERSON></a>
                            <a href="#rsvp" class="btn btn-sm btn-light scrollto">Visszajelzés</a>
                            <a href="?lang=en" class="btn btn-sm" style="color: #fff; display: flex; align-items: center; gap: 4px; padding: 0 10px; margin: 10px 5px; width: 50px">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 60 30">
                                <rect width="60" height="30" fill="#012169"/>
                                <polygon points="0,0 25,15 0,30" fill="#FFF"/>
                                <polygon points="60,0 35,15 60,30" fill="#FFF"/>
                                <polygon points="0,0 30,15 0,30" fill="#C8102E"/>
                                <polygon points="60,0 30,15 60,30" fill="#C8102E"/>
                                <rect x="25" width="10" height="30" fill="#FFF"/>
                                <rect y="10" width="60" height="10" fill="#FFF"/>
                                <rect x="27" width="6" height="30" fill="#C8102E"/>
                                <rect y="12" width="60" height="6" fill="#C8102E"/>
                                </svg>
                                EN
                            </a>
                            <a href="?lang=hu" class="btn btn-sm" style="color: #fff; display: flex; align-items: center; gap: 4px; padding: 0 10px; margin: 10px 5px; width: 50px">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 3 2">
                                <rect width="3" height="2" fill="#fff"/>
                                <rect width="3" height="0.6667" y="0" fill="#cd2a3e"/>
                                <rect width="3" height="0.6667" y="1.3333" fill="#436f4d"/>
                                </svg>
                                HU
                            </a>

                            
                            
    
                            <button id="nav-mobile-btn"><i class="fas fa-bars"></i></button><!-- Mobile menu button -->
                        </nav>
                        <!-- END MAIN MENU -->
    
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- END HEADER -->
    
    
    <!-- BEGIN HERO SECTION -->
    <div id="hero" class="bg-slideshow section-divider-bottom-1 section-divider-bg-color">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <div class="v-center">
                    <div class="hero-divider-top light" data-animation-direction="from-top" data-animation-delay="700"></div>
                    
                    <h1 class="hero-title light">
                        <span data-animation-direction="from-right" data-animation-delay="300">Flóra</span>
                        <small data-animation-direction="from-top" data-animation-delay="300">&</small> 
                        <span data-animation-direction="from-left" data-animation-delay="300">Ádám</span>
                    </h1>
                    
                    <div class="hero-divider-bottom light" data-animation-direction="from-bottom" data-animation-delay="700"></div>
                    
                    <div class="hero-subtitle light">Összeházasodunk</div>
                    
                    <!-- 
                    Countdown container 
                    Use the data attribute "date" to set the countdown date. 
                    E.g.: data-date="2022/09/20 3:00 PM"
                    -->
                    <div class="countdown" data-date="2025/09/13 3:00 PM"></div>

                    <div data-animation-direction="fade" data-animation-delay="1000">
                        <a href="#rsvp" class="btn btn-light scrollto">Visszajelzés</a>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END HERO SECTION -->
    
    
    <!-- BEGIN BRIDE & GROOM SECTION -->
    <section class="section-bg-color overflow-content-over no-padding-top">
    
        <div class="section-bg-color overflow-content no-padding">
            <div class="container">
                <div class="row">
                    <div class="col overflow-image-wrapper">
                    
                        <div class="overflow-image-text extra-padding-top">
                            <h2 class="title">Úgy döntöttünk, hogy most már örökre bírni fogjuk egymást.</h2>
                            <p class="center">Gyertek velünk nevetni, sírni, és tortát enni!</p>
                            <p>&nbsp;</p>
                            <p>&nbsp;</p>
                        </div>
                        
                        <div class="overflow-image flower">
                            <img src="photos/image8.jpg" alt="Couple Photo">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </section>
    <!-- END BRIDE & GROOM SECTION -->

    <!-- BEGIN TIMELINE SECTION -->
    <section id="loveline" class="section-bg-color">
        <div class="container">
            <div class="row">
                <div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
                    <div class="timeline">		
                        <div class="template-1">
    
                            <div class="image-1" data-parallax="-4" data-animation-direction="from-left"
                                data-animation-delay="250">
                                <img src="photos/image19.jpg" alt="">
                            </div>
    
                            <div class="image-2" data-parallax="6" data-animation-direction="from-right"
                                data-animation-delay="250">
                                <img src="photos/image13.jpg" alt="">
                            </div>
    
                            <div class="description-wrapper" data-parallax="-6" data-animation-direction="from-bottom"
                                data-animation-delay="250">
                                <div class="description" data-parallax="-6" data-animation-direction="from-bottom"
                                    data-animation-delay="250">
                                    <img src="photos/image17.jpg" alt="">
                                </div>
                            </div>
                        </div>
                    </div>		
                </div>
            </div>
        </div>
    </section>
    <!-- END TIMELINE SECTION -->
    
    
    <!-- BEGIN WEDDING INVITE SECTION -->
    <section id="the-wedding" class="parallax-background bg-color-overlay padding-divider-top">
        <div class="section-divider-top-1 section-divider-bg-color off-section"></div><!-- The class "section-divider-top-1" can also be applied to the tag <section>. In this case, it was added on a new <div> because the tag <section> have all pseudo elements (::after and ::before) in use. -->
        <div class="container">
            <!--<div class="row">
                <div class="col-sm-12">
                    <h2 class="section-title light">We're getting married</h2>
                </div>
            </div>-->
            
            <div class="row">
                <div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2 center">
                    <div class="invite neela-style" data-animation-direction="from-left" data-animation-delay="100">
                        <div class="invite_title">
                            <div class="text">
                                Save<small>the</small>Date
                            </div>
                        </div>
                        
                        <div class="invite_info">
                            <h2>Flóra <small>&</small> Ádám</h2>
                            
                            <div class="uppercase">Szeretettel várunk az esküvőnkre</div>
                            <div class="date">2025 szeptember 13.<small>14:30-kor</small></div>
                            <div class="uppercase">Szokolya,<br>Királyréti Vadászkastély</div>
                            
                            <!--<h5>Reception to follow</h5>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END WEDDING INVITE SECTION -->

    <!-- BEGIN TIMELINE SECTION -->
    <section id="loveline" class="section-bg-color">
        <div class="container">
            <div class="row">
                <div class="col-md-12 col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
                    <div class="timeline">
    
                        <div class="template-1">
    
                            <div class="image-1" data-parallax="-4" data-animation-direction="from-left"
                                data-animation-delay="250">
                                <a href="invite/image20.jpg" target="_blank">
                                    <img src="invite/image20.jpg" alt="">
                                </a>
                            </div>
    
                            <div class="image-2" data-parallax="6" data-animation-direction="from-right"
                                data-animation-delay="250">
                                <a href="invite/image3.jpg" target="_blank">
                                    <img src="invite/image3.jpg" alt="">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END TIMELINE SECTION -->

    <!-- BEGIN MENU SECTION -->
    <section class="menu-section no-padding-top no-padding-bottom">
        <div id="menu-image" class="half-img"></div>
    
        <div class="container">
            <div class="row">
                <div class="col-xl-6 offset-xl-6 col-lg-8 offset-lg-4 menu-wrapper">
                    <div class="neela-style">
                        <div class="menu-top-flowers"></div>
    
                        <h2 class="section-title-lg">Program</h2>
    
                        <ul class="menu-items">
                            <li>
                                <h3>14:30</h3>
                                <h4>Vendégvárás</h4>
                            </li>
                            <li>
                                <h3>15:30</h3>
                                <h4>Ceremónia</h4>
                            </li>
                            <li>
                                <h3>16:00</h3>
                                <h4>Fotózás</h4>
                            </li>
                            <li>
                                <h3>19:00</h3>
                                <h4>Vacsora</h4>
                            </li>
                            <li>
                                <h3>23:00</h3>
                                <h4>Torta</h4>
                            </li>
                            <li>
                                <h3>0:00</h3>
                                <h4>Éjféli vacsora</h4>
                            </li>
                            <li>
                                <h3>0:45</h3>
                                <h4>Moshpit hajnalig</h4>
                            </li>
                        </ul>
    
                        <div class="menu-bottom-flowers"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END MENU SECTION -->

            <!-- BEGIN WEDDING DETAILS SECTION --
            <section id="wedding-details" class="bg-color">
                <div class="container">
                    <div class="row">
                        <div class="col-md-2 wedding-details light">
                            &nbsp;
                            </div>
                        <div class="col-md-4 wedding-details light">
                            <i class="icon-champagne-glasses"></i>
                            <h4>Parkolás</h4>
                            <p>Parkolás space at the venue is limited. You can also park for free in the lot across the road or at the Turista Szálló
                            (Tourist Lodge) parking area just opposite.</p>
                        </div>
            
                        <div class="col-md-4 wedding-details light">
                            <i class="icon-champagne-glasses"></i>
                            <h4>Accomodation</h4>
                            <p>100 főig tudunk szállást biztosítani. Kérünk, hogy jelezd előre, ha maradnál éjszakára!</p>
                        </div>
                    </div>
                </div>
            </section>
    <!-- END WEDDING DETAILS SECTION -->

    <!-- BEGIN WEDDING LOCATION SECTION -->
    <section id="location">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <h2 class="section-title">Helyszín</h2>
                </div>
            </div>
        </div>
    
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-xl-10 offset-xl-1">
    
                    <div class="map-info-container">
                        <div class="info-wrapper" data-animation-direction="from-bottom" data-animation-delay="100">
                            <div class="location-info">
                                <div class="neela-style">		
                                    <h4 class="has-icon"><i
                                            class="icon-diamond-ring"></i>
                                            Királyréti Vadászkastély
                                        </h4>
                                    <h5>Szokolya, Királyrét, 2624</h5>
                                    <p>
                                        47.89533180751165, 18.97720805857283
                                    </p>
                                    <div class="center">
                                        <a href="https://waze.com/ul?ll=47.89533180751165,18.97720805857283&navigate=yes&q=Királyréti%20Vadászkastély" class="btn btn-waze" style="background-color: #33CCFF; color: #fff">
                                            <img src="img/waze-icon.svg" alt="" style="width: 20px; height: 20px; margin-right: 5px;">
                                            Waze
                                        </a>
                                    </div>
                                    <div class="center">
                                        <a href="https://www.google.com/maps?q=47.89533180751165,18.97720805857283" target="_blank"
                                            class="btn btn-waze" style="background-color: #d23f31; color: #fff">
                                            <svg xmlns="http://www.w3.org/2000/svg" style="width: 20px; height: 20px; margin-right: 5px;" fill="currentColor" viewBox="0 0 24 24">
                                                <path
                                                    d="M12 2C8.1 2 5 5.1 5 9c0 5.2 7 13 7 13s7-7.8 7-13c0-3.9-3.1-7-7-7zm0 9.5c-1.4 0-2.5-1.1-2.5-2.5S10.6 6.5 12 6.5s2.5 1.1 2.5 2.5S13.4 11.5 12 11.5z" />
                                            </svg>
                                            Google Térkép
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
    
                        <div class="map-wrapper" data-animation-direction="fade" data-animation-delay="100">
                            <div id="map_canvas"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <h2 class="section-title">Szállás</h2>
                </div>
            </div>
        </div>
    
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-xl-10 offset-xl-1">
                    <p class="cta">
                        100 főig tudunk szállást biztosítani. Kérünk, hogy jelezd előre, ha maradnál éjszakára!
                    </p>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <h2 class="section-title">Parkolás</h2>
                </div>
            </div>
        </div>
    
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-xl-10 offset-xl-1">
                    <p class="cta">
                        Parkolás space at the venue is limited. You can also park for free in the lot across the road or at the Turista Szálló (Tourist Lodge) parking area just opposite.
                    </p>
    
    
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-xl-10 offset-xl-1">
                    <blockquote class="neela-quote" style="font-size: 16pt;">
                        A lakásárak elszálltak, de a szerelmünk változatlan. Ha nem turmixgépet hozol hanem borítékot, abba meg belefér egy négyzetméter!
                    </blockquote>
                </div>
            </div>
        </div>


    </section>
    <!-- END WEDDING INFO SECTION -->

    <!-- BEGIN GALLERY SECTION -->
    <section id="gallery" class="parallax-background bg-color-overlay padding-divider-top">
        <div class="section-divider-top-1 off-section"></div>
        <!-- The class "section-divider-top-1" can also be applied to the tag <section>. In this case, it was added on a new <div> because the tag <section> have all pseudo elements (::after and ::before) in use. -->
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <h1 class="section-title light">Mi (nem "Mi?", hanem "MI!"</h1>
                </div>
            </div>
        </div>
    
        <div class="gallery-wrapper">
            <div class="gallery-left"><i class="fas fa-chevron-left"></i></div>
            <div class="gallery-right"><i class="fas fa-chevron-right"></i></div>

            <?php
            $imagesPerRow = 5;
            $images = glob('photos/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);

            // Sort by numeric part (e.g., image1.jpg to image30.jpg)
            usort($images, function ($a, $b) {
                preg_match('/(\d+)/', basename($a), $aMatch);
                preg_match('/(\d+)/', basename($b), $bMatch);
                return ($aMatch[1] ?? 0) - ($bMatch[1] ?? 0);
            });

            $chunks = array_chunk($images, $imagesPerRow);
            ?>

            <div class="gallery-scroller">
                <?php foreach ($chunks as $chunk): ?>
                    <ul>
                        <?php foreach ($chunk as $image): ?>
                            <li>
                                <div class="hover-info">
                                    <a class="btn btn-light btn-sm only-icon" href="<?= $image ?>"
                                    data-lightbox="WeddingPhotos" title="Wedding Fotózás">
                                        <i class="fa fa-link"></i>
                                    </a>
                                </div>
                                <img src="<?= $image ?>" alt="" />
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endforeach; ?>
            </div>
                     
        </div>
    </section>
    <!-- END GALLERY SECTION -->

    <section id="rsvp" class="section-bg-color extra-padding-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 offset-lg-1 col-xl-8 offset-xl-2 col-xxl-8 offset-xxl-2">
                    <div class="form-wrapper flowers neela-style" x-data="rsvpForm">
                        <h2 class="section-title" x-show="!success">Jössz velünk ünnepelni?</h2>

                        <p x-show="!success">
                        Kérlek töltsd ki az űrlapot. További vendégeket is hozzáadhatsz a „Vendég hozzáadása” gombbal.
                        </p>
    
                        <form id="form-rsvp" @submit.prevent="submitForm" class="space-y-6" x-show="!success" x-transition:leave="transition ease-out duration-700"
                            x-transition:leave-start="opacity-100 translate-y-0"
                            x-transition:leave-end="opacity-0 translate-y-4">
    
                            <!-- Guest fields -->
                            <template x-for="(guest, index) in guests" :key="index">
                                <fieldset class="guest-fieldset border p-4 rounded mb-3">
                                    <legend class="font-semibold mb-2">Vendég #<span x-text="index + 1"></span></legend>
    
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" placeholder="Full name of attendee"
                                            x-model="guest.name" required>
                                        <label>Résztvevő teljes neve*</label>
                                    </div>

                                    <div class="mb-3">
                                        <p class="mb-2 fw-semibold">Követsz-e valamilyen speciális étrendet?</p>
                                    
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" :id="`diet_lactose_${index}`" value="lactose"
                                                x-model="guest.diet">
                                            <label class="form-check-label" :for="`diet_lactose_${index}`">
                                                Laktózmentes
                                            </label>
                                        </div>
                                    
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" :id="`diet_gluten_${index}`" value="gluten"
                                                x-model="guest.diet">
                                            <label class="form-check-label" :for="`diet_gluten_${index}`">
                                                Gluténmentes
                                            </label>
                                        </div>
                                    
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" :id="`diet_sugar_${index}`" value="sugar" x-model="guest.diet">
                                            <label class="form-check-label" :for="`diet_sugar_${index}`">
                                                Cukormentes
                                            </label>
                                        </div>
                                    
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" :id="`diet_vegetarian_${index}`" value="vegetarian"
                                                x-model="guest.diet">
                                            <label class="form-check-label" :for="`diet_vegetarian_${index}`">
                                                Vegetáriánus
                                            </label>
                                        </div>
                                    
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" :id="`diet_vegan_${index}`" value="vegan" x-model="guest.diet">
                                            <label class="form-check-label" :for="`diet_vegan_${index}`">
                                                Vegán
                                            </label>
                                        </div>
                                    
                                        <div class="form-check mt-2">
                                            <label class="form-check-label" :for="`diet_other_${index}`">
                                                Egyéb:
                                            </label>
                                            <input type="text" class="form-control d-inline-block ms-2" style="width: 120px; height: 30px"
                                                :id="`diet_other_${index}`" x-model="guest.diet_other">
                                        </div>
                                    </div>
                                      
    
                                    <div class="form-floating mb-2">
                                        <input type="text" class="form-control" placeholder="Any food allergies?"
                                            x-model="guest.allergy">
                                        <label>Van-e ételallergiád?</label>
                                    </div>
    
                                    <div class="text-end">
                                        <button type="button" class="btn btn-danger btn-sm" @click="guests.splice(index, 1)"
                                            x-show="index > 0">
                                            ✕ Vendég eltávolítása
                                        </button>
                                    </div>
                                </fieldset>
                            </template>
    
                            <!-- Add guest button -->
                            <div class="text-center mb-4">
                                <button type="button" class="btn btn-primary btn-success btn-add btn-custom"
                                    @click="guests.push({name:'',diet:[],diet_other:'',allergy:''})">
                                    Vendég hozzáadása
                                </button>
                            </div>
    
                            <!-- Additional options -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="high_chair" x-model="high_chair">
                                <label class="form-check-label" for="high_chair">
                                    Szükségem van etetőszékre.
                                </label>
                            </div>
    
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" placeholder="Your email address" x-model="contact"
                                    required>
                                <label>Email cím*</label>
                            </div>
    
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" placeholder="Szállás request"
                                    x-model="accommodation">
                                <label>Szeretnél-e szállást kérni? (A szállás korlátozott, és nem biztos, hogy 2 fős szobák állnak rendelkezésre.)</label>
                            </div>
    
                            <div class="form-floating mb-3">
                                <textarea class="form-control" rows="4" placeholder="Special requests"
                                    x-model="special_notes"></textarea>
                                <label>Van-e bármilyen egyéb speciális körülmény, amiről fontos tudnunk?</label>
                            </div>
    
                            <div class="form_status_message text-danger text-center" x-text="errorMessage"
                                x-show="errorMessage"></div>
    
                            <div class="center">
                                <button type="submit" class="btn btn-primary btn-success btn-custom" :disabled="isSubmitting"
                                    x-text="isSubmitting ? 'Please wait...' : 'Submit'">
                                    Küldés
                                </button>
                            </div>
                        </form>

                        <div class="successMessage" x-show="success" x-transition:enter="transition ease-out duration-700"
                            x-transition:enter-start="opacity-0 translate-y-4"
                            x-transition:enter-end="opacity-100 translate-y-0">
                            <h2 class="section-title">Köszönjük a visszajelzést!</h2>
                            <h3>Már alig várjuk, hogy együtt ünnepelhessünk!</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
      
    
    
    <!-- BEGIN FOOTER -->
    <footer id="footer">
    
        <div class="copyright">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12">
                        &copy; 2025 Flóra & Ádám
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- END FOOTER -->
    
</div>