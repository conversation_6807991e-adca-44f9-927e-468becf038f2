<?php

$inputFile = 'home.en.php';
$outputFile = 'home.hu.php';
$backupFile = 'home.hu.php.backup-' . date('ymdHi');
$i18nFile = 'i18n.json';

// Ellenőrzések
if (!file_exists($inputFile)) exit("Hiba: $inputFile nem található.\n");
if (!file_exists($i18nFile)) exit("Hiba: $i18nFile nem található.\n");

// <PERSON>, ha már létezik a magyar fájl
if (file_exists($outputFile)) {
    if (!rename($outputFile, $backupFile)) {
        exit("Hiba: Nem sikerült menteni a korábbi $outputFile mentését ($backupFile).\n");
    }
    echo "Mentés készítve: $backupFile\n";
}

// Nyelvi fájl beolvasása
$i18n = json_decode(file_get_contents($i18nFile), true);
if (!isset($i18n['en'], $i18n['hu'])) {
    exit("Hiba: Az i18n.json nem tartalmaz 'en' és 'hu' kulcsokat.\n");
}

// Forrásfájl beolvasása
$content = file_get_contents($inputFile);

// Fordítás biztonságos regex-szel
foreach ($i18n['en'] as $key => $en) {
    if (!isset($i18n['hu'][$key])) continue;
    $hu = $i18n['hu'][$key];

    // Escape regex karakterek
    $pattern = preg_quote($en, '/');

    // Csak akkor cseréljük, ha nem szöveg belsejében van (pl. változónévben)
    // Feltétel: előtte vagy utána whitespace, HTML tag, írásjel, szóhatár
    $regex = '/(?<=^|[\s>="\(\{\[;])' . $pattern . '(?=$|[\s<="\)\}\];,.!?])/u';

    // Csere
    $content = preg_replace($regex, $hu, $content);
}

// Eredmény mentése
file_put_contents($outputFile, $content);
echo "Fordítás elkészült: $outputFile\n";
